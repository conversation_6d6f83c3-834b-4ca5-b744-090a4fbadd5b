﻿'use client';

import Link from 'next/link';
import { ShoppingCart, User, Menu, LogOut } from 'lucide-react';

export function Header() {
  return (
    <header className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="text-2xl font-bold text-slate-900">
              doCorpo
            </div>
          </Link>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/" className="text-slate-600 hover:text-slate-900 transition-colors">
              Início
            </Link>
            <Link href="/produtos" className="text-slate-600 hover:text-slate-900 transition-colors">
              Produtos
            </Link>
            <Link href="/carrinho" className="text-slate-600 hover:text-slate-900 transition-colors">
              Carrinho
            </Link>
            <Link href="/admin" className="text-slate-600 hover:text-slate-900 transition-colors">
              Admin
            </Link>
          </nav>

          {/* Right side */}
          <div className="flex items-center space-x-4">
            {/* Cart */}
            <Link href="/carrinho" className="relative">
              <button className="p-2 text-slate-600 hover:text-slate-900">
                <ShoppingCart className="h-5 w-5" />
              </button>
            </Link>

            {/* User menu */}
            <div className="flex items-center space-x-2">
              <Link href="/login">
                <button className="px-4 py-2 text-slate-600 hover:text-slate-900">
                  Entrar
                </button>
              </Link>
              <Link href="/cadastro">
                <button className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                  Cadastrar
                </button>
              </Link>
            </div>

            {/* Mobile menu */}
            <button className="md:hidden p-2 text-slate-600 hover:text-slate-900">
              <Menu className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </header>
  );
}
