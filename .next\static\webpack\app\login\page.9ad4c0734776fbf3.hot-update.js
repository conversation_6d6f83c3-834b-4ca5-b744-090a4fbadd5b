"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/login/page",{

/***/ "(app-pages-browser)/./app/login/page.tsx":
/*!****************************!*\
  !*** ./app/login/page.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _src_lib_auth__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/src/lib/auth */ \"(app-pages-browser)/./src/lib/auth.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@1.7.4_react-dom@19.2.0_react@19.2.0__react@19.2.0/node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction LoginPage() {\n    _s();\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        try {\n            const { user, error } = await (0,_src_lib_auth__WEBPACK_IMPORTED_MODULE_7__.signIn)(email, password);\n            if (error) {\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error('Erro ao fazer login: ' + error);\n            } else if (user) {\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.success('Login realizado com sucesso!');\n                router.push('/');\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error('Erro inesperado ao fazer login');\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n            className: \"w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                            className: \"text-2xl font-bold\",\n                            children: \"Entrar\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\docorpo\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                            children: \"Entre na sua conta doCorpo\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\docorpo\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\docorpo\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"email\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"E-mail\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\docorpo\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            id: \"email\",\n                                            type: \"email\",\n                                            value: email,\n                                            onChange: (e)=>setEmail(e.target.value),\n                                            required: true,\n                                            placeholder: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\docorpo\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\docorpo\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"password\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"Senha\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\docorpo\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            id: \"password\",\n                                            type: \"password\",\n                                            value: password,\n                                            onChange: (e)=>setPassword(e.target.value),\n                                            required: true,\n                                            placeholder: \"Sua senha\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\docorpo\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\docorpo\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\docorpo\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardFooter, {\n                            className: \"flex flex-col space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    type: \"submit\",\n                                    className: \"w-full\",\n                                    disabled: loading,\n                                    children: loading ? 'Entrando...' : 'Entrar'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\docorpo\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center text-sm text-gray-600\",\n                                    children: [\n                                        \"N\\xe3o tem uma conta?\",\n                                        ' ',\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/cadastro\",\n                                            className: \"text-blue-600 hover:underline\",\n                                            children: \"Cadastre-se\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\docorpo\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\docorpo\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\docorpo\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\docorpo\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\docorpo\\\\app\\\\login\\\\page.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\docorpo\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginPage, \"c9qDMR4Mdfo2euQQM0fbXdv2G00=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/login/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getUserData: () => (/* binding */ getUserData),\n/* harmony export */   onAuthChange: () => (/* binding */ onAuthChange),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   signUp: () => (/* binding */ signUp)\n/* harmony export */ });\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/.pnpm/firebase@10.14.1/node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/.pnpm/firebase@10.14.1/node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _firebase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n\n\n\nasync function signIn(email, password) {\n    try {\n        console.log(' Tentando fazer login com:', email);\n        console.log(' Auth object:', _firebase__WEBPACK_IMPORTED_MODULE_2__.auth);\n        console.log(' Firebase config:', _firebase__WEBPACK_IMPORTED_MODULE_2__.auth.config);\n        const result = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signInWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, email, password);\n        console.log(' Login realizado com sucesso:', result.user.uid);\n        return {\n            user: result.user,\n            error: null\n        };\n    } catch (error) {\n        console.error(' Erro no login:', error);\n        console.error(' Código do erro:', error.code);\n        console.error(' Mensagem:', error.message);\n        return {\n            user: null,\n            error: error.message\n        };\n    }\n}\nasync function signUp(email, password, name, phone) {\n    try {\n        console.log(' Tentando criar usuário:', email);\n        const result = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.createUserWithEmailAndPassword)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, email, password);\n        // Criar documento do usuário no Firestore\n        const userData = {\n            name,\n            email,\n            phone,\n            role: 'customer',\n            createdAt: (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.serverTimestamp)()\n        };\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, 'users', result.user.uid), userData);\n        return {\n            user: result.user,\n            error: null\n        };\n    } catch (error) {\n        console.error(' Erro no cadastro:', error);\n        return {\n            user: null,\n            error: error.message\n        };\n    }\n}\nasync function signOut() {\n    try {\n        await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.signOut)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth);\n        return {\n            error: null\n        };\n    } catch (error) {\n        return {\n            error: error.message\n        };\n    }\n}\nasync function getUserData(uid) {\n    try {\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_1__.doc)(_firebase__WEBPACK_IMPORTED_MODULE_2__.db, 'users', uid));\n        if (userDoc.exists()) {\n            return {\n                id: uid,\n                ...userDoc.data()\n            };\n        }\n        return null;\n    } catch (error) {\n        console.error('Erro ao buscar dados do usuário:', error);\n        return null;\n    }\n}\nfunction onAuthChange(callback) {\n    return (0,firebase_auth__WEBPACK_IMPORTED_MODULE_0__.onAuthStateChanged)(_firebase__WEBPACK_IMPORTED_MODULE_2__.auth, callback);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/auth.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(app-pages-browser)/./node_modules/.pnpm/firebase@10.14.1/node_modules/firebase/app/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/.pnpm/firebase@10.14.1/node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/.pnpm/firebase@10.14.1/node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/.pnpm/firebase@10.14.1/node_modules/firebase/storage/dist/esm/index.esm.js\");\n\n\n\n\nconst firebaseConfig = {\n    apiKey: \"AIzaSyDNB2kaxHwdayO5w2jC4_Bnrvz3_B9Eeqs\",\n    authDomain: \"zmt3utihduf0uxvorbdvk75r9e29pf.firebaseapp.com\",\n    projectId: \"zmt3utihduf0uxvorbdvk75r9e29pf\",\n    storageBucket: \"zmt3utihduf0uxvorbdvk75r9e29pf.firebasestorage.app\",\n    messagingSenderId: \"491725974763\",\n    appId: \"1:491725974763:web:351229f024e5c496bfa2ae\",\n    measurementId: \"G-75MH7DKQ86\"\n};\n// Initialize Firebase\nconst app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\n// Initialize Firebase services\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\nconst storage = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.getStorage)(app);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/firebase.ts\n"));

/***/ })

});