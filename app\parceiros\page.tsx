'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Header } from '@/src/components/Header';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Store, 
  TrendingUp, 
  Shield, 
  Users, 
  Package, 
  CreditCard, 
  Truck, 
  BarChart3,
  CheckCircle,
  ArrowRight,
  Mail,
  Phone,
  Building
} from 'lucide-react';
import { toast } from 'sonner';

export default function ParceirosPage() {
  const [formData, setFormData] = useState({
    nome: '',
    empresa: '',
    email: '',
    telefone: '',
    categoria: '',
    experiencia: '',
    mensagem: ''
  });
  const [loading, setLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Simular envio de email (aqui você integraria com um serviço de email)
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast.success('Solicitação enviada com sucesso! Entraremos em contato em breve.');
      setFormData({
        nome: '',
        empresa: '',
        email: '',
        telefone: '',
        categoria: '',
        experiencia: '',
        mensagem: ''
      });
    } catch (error) {
      toast.error('Erro ao enviar solicitação. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-yellow-500/5 via-transparent to-yellow-500/5"></div>
      
      <Header />

      <main className="relative z-10">
        {/* Hero Section */}
        <section className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 bg-clip-text text-transparent">
              Seja um Parceiro doCorpo
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
              Coloque seus produtos à venda em nossa plataforma e aproveite todo nosso sistema de vendas, logística e marketing
            </p>
            <p className="text-lg text-yellow-400 mb-12 font-medium">
              ✨ Venda sob a marca doCorpo com todo suporte necessário ✨
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button 
                onClick={() => document.getElementById('formulario')?.scrollIntoView({ behavior: 'smooth' })}
                className="w-full sm:w-auto px-8 py-4 text-lg bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-black font-semibold rounded-lg shadow-lg hover:shadow-yellow-500/25 transition-all duration-200"
              >
                Quero Ser Parceiro
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
              
              <Link href="/">
                <Button 
                  variant="outline" 
                  className="w-full sm:w-auto px-8 py-4 text-lg border-yellow-500/50 text-yellow-400 hover:bg-yellow-500/10 hover:text-yellow-300 hover:border-yellow-400 transition-all duration-200"
                >
                  Voltar ao Início
                </Button>
              </Link>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20 backdrop-blur-sm">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">Por que ser Parceiro doCorpo?</h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Oferecemos uma plataforma completa para você vender seus produtos com toda infraestrutura necessária
              </p>
            </div>

            {/* Benefits Grid */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
              <div className="text-center p-8 bg-black/40 rounded-xl border border-yellow-500/20 backdrop-blur-sm">
                <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg shadow-yellow-500/25">
                  <Store className="w-8 h-8 text-black" />
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">Vitrine Digital</h3>
                <p className="text-gray-300 text-lg">Seus produtos expostos em nossa plataforma premium com alta visibilidade</p>
              </div>

              <div className="text-center p-8 bg-black/40 rounded-xl border border-yellow-500/20 backdrop-blur-sm">
                <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg shadow-yellow-500/25">
                  <CreditCard className="w-8 h-8 text-black" />
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">Sistema de Pagamento</h3>
                <p className="text-gray-300 text-lg">Processamento seguro de pagamentos e gestão financeira completa</p>
              </div>

              <div className="text-center p-8 bg-black/40 rounded-xl border border-yellow-500/20 backdrop-blur-sm">
                <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg shadow-yellow-500/25">
                  <Truck className="w-8 h-8 text-black" />
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">Logística Integrada</h3>
                <p className="text-gray-300 text-lg">Gestão completa de estoque, envios e entregas para seus clientes</p>
              </div>

              <div className="text-center p-8 bg-black/40 rounded-xl border border-yellow-500/20 backdrop-blur-sm">
                <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg shadow-yellow-500/25">
                  <BarChart3 className="w-8 h-8 text-black" />
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">Relatórios e Analytics</h3>
                <p className="text-gray-300 text-lg">Acompanhe vendas, performance e insights detalhados em tempo real</p>
              </div>

              <div className="text-center p-8 bg-black/40 rounded-xl border border-yellow-500/20 backdrop-blur-sm">
                <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg shadow-yellow-500/25">
                  <Users className="w-8 h-8 text-black" />
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">Suporte Dedicado</h3>
                <p className="text-gray-300 text-lg">Equipe especializada para ajudar em todas as etapas do processo</p>
              </div>

              <div className="text-center p-8 bg-black/40 rounded-xl border border-yellow-500/20 backdrop-blur-sm">
                <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg shadow-yellow-500/25">
                  <Shield className="w-8 h-8 text-black" />
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">Marca Consolidada</h3>
                <p className="text-gray-300 text-lg">Aproveite a credibilidade e reconhecimento da marca doCorpo</p>
              </div>
            </div>
          </div>
        </section>

        {/* Contact Form Section */}
        <section id="formulario" className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">Solicite sua Parceria</h2>
              <p className="text-xl text-gray-300 max-w-2xl mx-auto">
                Preencha o formulário abaixo e nossa equipe entrará em contato para discutir os detalhes da parceria
              </p>
            </div>

            <Card className="bg-black/40 border-yellow-500/20 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-2xl text-white text-center">Formulário de Parceria</CardTitle>
                <CardDescription className="text-gray-300 text-center">
                  Todas as informações são confidenciais e serão usadas apenas para análise da parceria
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Nome Completo *
                      </label>
                      <Input
                        name="nome"
                        value={formData.nome}
                        onChange={handleChange}
                        required
                        className="bg-gray-800/50 border-gray-600 text-white placeholder-gray-400 focus:border-yellow-500"
                        placeholder="Seu nome completo"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Nome da Empresa *
                      </label>
                      <Input
                        name="empresa"
                        value={formData.empresa}
                        onChange={handleChange}
                        required
                        className="bg-gray-800/50 border-gray-600 text-white placeholder-gray-400 focus:border-yellow-500"
                        placeholder="Nome da sua empresa"
                      />
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Email *
                      </label>
                      <Input
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleChange}
                        required
                        className="bg-gray-800/50 border-gray-600 text-white placeholder-gray-400 focus:border-yellow-500"
                        placeholder="<EMAIL>"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Telefone *
                      </label>
                      <Input
                        name="telefone"
                        value={formData.telefone}
                        onChange={handleChange}
                        required
                        className="bg-gray-800/50 border-gray-600 text-white placeholder-gray-400 focus:border-yellow-500"
                        placeholder="(11) 99999-9999"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Categoria de Produtos *
                    </label>
                    <select
                      name="categoria"
                      value={formData.categoria}
                      onChange={handleChange}
                      required
                      className="w-full bg-gray-800/50 border border-gray-600 text-white rounded-md px-3 py-2 focus:border-yellow-500 focus:outline-none"
                    >
                      <option value="">Selecione uma categoria</option>
                      <option value="roupas">Roupas e Acessórios</option>
                      <option value="calcados">Calçados</option>
                      <option value="bolsas">Bolsas e Carteiras</option>
                      <option value="joias">Joias e Bijuterias</option>
                      <option value="perfumes">Perfumes e Cosméticos</option>
                      <option value="casa">Casa e Decoração</option>
                      <option value="eletronicos">Eletrônicos</option>
                      <option value="outros">Outros</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Experiência no Mercado *
                    </label>
                    <select
                      name="experiencia"
                      value={formData.experiencia}
                      onChange={handleChange}
                      required
                      className="w-full bg-gray-800/50 border border-gray-600 text-white rounded-md px-3 py-2 focus:border-yellow-500 focus:outline-none"
                    >
                      <option value="">Selecione sua experiência</option>
                      <option value="iniciante">Iniciante (menos de 1 ano)</option>
                      <option value="intermediario">Intermediário (1-3 anos)</option>
                      <option value="experiente">Experiente (3-5 anos)</option>
                      <option value="veterano">Veterano (mais de 5 anos)</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Conte-nos sobre seus produtos e objetivos
                    </label>
                    <Textarea
                      name="mensagem"
                      value={formData.mensagem}
                      onChange={handleChange}
                      rows={4}
                      className="bg-gray-800/50 border-gray-600 text-white placeholder-gray-400 focus:border-yellow-500"
                      placeholder="Descreva seus produtos, volume de vendas atual, objetivos com a parceria, etc."
                    />
                  </div>

                  <div className="text-center">
                    <Button
                      type="submit"
                      disabled={loading}
                      className="w-full md:w-auto px-12 py-3 text-lg bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-black font-semibold shadow-lg hover:shadow-yellow-500/25 transition-all duration-200"
                    >
                      {loading ? (
                        <>
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-black mr-2"></div>
                          Enviando...
                        </>
                      ) : (
                        <>
                          <Mail className="w-5 h-5 mr-2" />
                          Enviar Solicitação
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Contact Info Section */}
        <section className="py-16 px-4 sm:px-6 lg:px-8 bg-black/20 backdrop-blur-sm">
          <div className="max-w-4xl mx-auto text-center">
            <h3 className="text-2xl font-bold text-white mb-8">Outras Formas de Contato</h3>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="flex flex-col items-center">
                <div className="w-12 h-12 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mb-4">
                  <Mail className="w-6 h-6 text-black" />
                </div>
                <h4 className="text-lg font-semibold text-white mb-2">Email</h4>
                <p className="text-gray-300"><EMAIL></p>
              </div>

              <div className="flex flex-col items-center">
                <div className="w-12 h-12 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mb-4">
                  <Phone className="w-6 h-6 text-black" />
                </div>
                <h4 className="text-lg font-semibold text-white mb-2">Telefone</h4>
                <p className="text-gray-300">(11) 9999-9999</p>
              </div>

              <div className="flex flex-col items-center">
                <div className="w-12 h-12 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mb-4">
                  <Building className="w-6 h-6 text-black" />
                </div>
                <h4 className="text-lg font-semibold text-white mb-2">Escritório</h4>
                <p className="text-gray-300">São Paulo, SP</p>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
}
