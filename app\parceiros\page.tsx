'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Header } from '@/src/components/Header';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Store, 
  TrendingUp, 
  Shield, 
  Users, 
  Package, 
  CreditCard, 
  Truck, 
  BarChart3,
  CheckCircle,
  ArrowRight,
  Mail,
  Phone,
  Building
} from 'lucide-react';
import { toast } from 'sonner';

export default function ParceirosPage() {
  const [formData, setFormData] = useState({
    nome: '',
    empresa: '',
    email: '',
    telefone: '',
    categoria: '',
    experiencia: '',
    mensagem: ''
  });
  const [loading, setLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Simular envio de email (aqui você integraria com um serviço de email)
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast.success('Solicitação enviada com sucesso! Entraremos em contato em breve.');
      setFormData({
        nome: '',
        empresa: '',
        email: '',
        telefone: '',
        categoria: '',
        experiencia: '',
        mensagem: ''
      });
    } catch (error) {
      toast.error('Erro ao enviar solicitação. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-yellow-500/5 via-transparent to-yellow-500/5"></div>
      
      <Header />

      <main className="relative z-10">
        {/* Hero Section */}
        <section className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 bg-clip-text text-transparent">
              Seja um Parceiro doCorpo
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto">
              Coloque seus produtos à venda em nossa plataforma e aproveite todo nosso sistema de vendas, logística e marketing
            </p>
            <p className="text-lg text-yellow-400 mb-12 font-medium">
              ✨ Venda sob a marca doCorpo com todo suporte necessário ✨
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button 
                onClick={() => document.getElementById('formulario')?.scrollIntoView({ behavior: 'smooth' })}
                className="w-full sm:w-auto px-8 py-4 text-lg bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-black font-semibold rounded-lg shadow-lg hover:shadow-yellow-500/25 transition-all duration-200"
              >
                Quero Ser Parceiro
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
              
              <Link href="/">
                <Button 
                  variant="outline" 
                  className="w-full sm:w-auto px-8 py-4 text-lg border-yellow-500/50 text-yellow-400 hover:bg-yellow-500/10 hover:text-yellow-300 hover:border-yellow-400 transition-all duration-200"
                >
                  Voltar ao Início
                </Button>
              </Link>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20 backdrop-blur-sm">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">Por que ser Parceiro doCorpo?</h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Oferecemos uma plataforma completa para você vender seus produtos com toda infraestrutura necessária
              </p>
            </div>

            {/* Benefits Grid */}
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
              <div className="text-center p-8 bg-black/40 rounded-xl border border-yellow-500/20 backdrop-blur-sm">
                <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg shadow-yellow-500/25">
                  <Store className="w-8 h-8 text-black" />
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">Vitrine Digital</h3>
                <p className="text-gray-300 text-lg">Seus produtos expostos em nossa plataforma premium com alta visibilidade</p>
              </div>

              <div className="text-center p-8 bg-black/40 rounded-xl border border-yellow-500/20 backdrop-blur-sm">
                <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg shadow-yellow-500/25">
                  <CreditCard className="w-8 h-8 text-black" />
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">Sistema de Pagamento</h3>
                <p className="text-gray-300 text-lg">Processamento seguro de pagamentos e gestão financeira completa</p>
              </div>

              <div className="text-center p-8 bg-black/40 rounded-xl border border-yellow-500/20 backdrop-blur-sm">
                <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg shadow-yellow-500/25">
                  <Truck className="w-8 h-8 text-black" />
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">Logística Integrada</h3>
                <p className="text-gray-300 text-lg">Gestão completa de estoque, envios e entregas para seus clientes</p>
              </div>

              <div className="text-center p-8 bg-black/40 rounded-xl border border-yellow-500/20 backdrop-blur-sm">
                <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg shadow-yellow-500/25">
                  <BarChart3 className="w-8 h-8 text-black" />
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">Relatórios e Analytics</h3>
                <p className="text-gray-300 text-lg">Acompanhe vendas, performance e insights detalhados em tempo real</p>
              </div>

              <div className="text-center p-8 bg-black/40 rounded-xl border border-yellow-500/20 backdrop-blur-sm">
                <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg shadow-yellow-500/25">
                  <Users className="w-8 h-8 text-black" />
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">Suporte Dedicado</h3>
                <p className="text-gray-300 text-lg">Equipe especializada para ajudar em todas as etapas do processo</p>
              </div>

              <div className="text-center p-8 bg-black/40 rounded-xl border border-yellow-500/20 backdrop-blur-sm">
                <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg shadow-yellow-500/25">
                  <Shield className="w-8 h-8 text-black" />
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">Marca Consolidada</h3>
                <p className="text-gray-300 text-lg">Aproveite a credibilidade e reconhecimento da marca doCorpo</p>
              </div>
            </div>
          </div>
        </section>
