﻿import { 
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut as firebaseSignOut,
  onAuthStateChanged,
  User as FirebaseUser
} from 'firebase/auth';
import { doc, getDoc, setDoc, serverTimestamp } from 'firebase/firestore';
import { auth, db } from './firebase';
import { User } from '@/types/db';

export async function signIn(email: string, password: string) {
  try {
    console.log(' Tentando fazer login com:', email);
    console.log(' Auth object:', auth);
    console.log(' Firebase config:', auth.config);
    
    const result = await signInWithEmailAndPassword(auth, email, password);
    console.log(' Login realizado com sucesso:', result.user.uid);
    
    return { user: result.user, error: null };
  } catch (error: any) {
    console.error(' Erro no login:', error);
    console.error(' Código do erro:', error.code);
    console.error(' Mensagem:', error.message);
    
    return { user: null, error: error.message };
  }
}

export async function signUp(email: string, password: string, name: string, phone?: string) {     
  try {
    console.log(' Tentando criar usuário:', email);
    const result = await createUserWithEmailAndPassword(auth, email, password);

    // Criar documento do usuário no Firestore
    const userData: Omit<User, 'id'> = {
      name,
      email,
      phone,
      role: 'customer',
      createdAt: serverTimestamp() as any
    };

    await setDoc(doc(db, 'users', result.user.uid), userData);

    return { user: result.user, error: null };
  } catch (error: any) {
    console.error(' Erro no cadastro:', error);
    return { user: null, error: error.message };
  }
}

export async function signOut() {
  try {
    await firebaseSignOut(auth);
    return { error: null };
  } catch (error: any) {
    return { error: error.message };
  }
}

export async function getUserData(uid: string): Promise<User | null> {
  try {
    const userDoc = await getDoc(doc(db, 'users', uid));
    if (userDoc.exists()) {
      return { id: uid, ...userDoc.data() } as User;
    }
    return null;
  } catch (error) {
    console.error('Erro ao buscar dados do usuário:', error);
    return null;
  }
}

export function onAuthChange(callback: (user: FirebaseUser | null) => void) {
  return onAuthStateChanged(auth, callback);
}
