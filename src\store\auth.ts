import { create } from 'zustand';
import { User as FirebaseUser } from 'firebase/auth';
import { User } from '@/src/types/db';

interface AuthState {
  user: FirebaseUser | null;
  userData: User | null;
  loading: boolean;
  setUser: (user: FirebaseUser | null) => void;
  setUserData: (userData: User | null) => void;
  setLoading: (loading: boolean) => void;
  isAdmin: () => boolean;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  userData: null,
  loading: true,
  setUser: (user) => set({ user }),
  setUserData: (userData) => set({ userData }),
  setLoading: (loading) => set({ loading }),
  isAdmin: () => get().userData?.role === 'admin'
}));
