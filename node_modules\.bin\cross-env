#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Downloads/docorpo/node_modules/.pnpm/cross-env@10.1.0/node_modules/cross-env/dist/bin/node_modules:/mnt/c/Users/<USER>/Downloads/docorpo/node_modules/.pnpm/cross-env@10.1.0/node_modules/cross-env/dist/node_modules:/mnt/c/Users/<USER>/Downloads/docorpo/node_modules/.pnpm/cross-env@10.1.0/node_modules/cross-env/node_modules:/mnt/c/Users/<USER>/Downloads/docorpo/node_modules/.pnpm/cross-env@10.1.0/node_modules:/mnt/c/Users/<USER>/Downloads/docorpo/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Downloads/docorpo/node_modules/.pnpm/cross-env@10.1.0/node_modules/cross-env/dist/bin/node_modules:/mnt/c/Users/<USER>/Downloads/docorpo/node_modules/.pnpm/cross-env@10.1.0/node_modules/cross-env/dist/node_modules:/mnt/c/Users/<USER>/Downloads/docorpo/node_modules/.pnpm/cross-env@10.1.0/node_modules/cross-env/node_modules:/mnt/c/Users/<USER>/Downloads/docorpo/node_modules/.pnpm/cross-env@10.1.0/node_modules:/mnt/c/Users/<USER>/Downloads/docorpo/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../cross-env/dist/bin/cross-env.js" "$@"
else
  exec node  "$basedir/../cross-env/dist/bin/cross-env.js" "$@"
fi
