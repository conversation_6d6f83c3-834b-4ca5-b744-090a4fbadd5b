'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { doc, updateDoc } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import {
  User,
  Mail,
  Phone,
  Save,
  MapPin,
  Building,
  Camera,
  Store,
  FileText,
  CreditCard,
  Users
} from 'lucide-react';
import { db, storage } from '@/src/lib/firebase';
import Header from '@/src/components/Header';
import { Button } from '@/src/components/ui/button';
import { Input } from '@/src/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/src/components/ui/card';
import { Badge } from '@/src/components/ui/badge';
import { useAuthStore } from '@/src/store/auth';
import { toast } from 'sonner';

interface ProfileFormData {
  name: string;
  phone: string;
  profilePhoto: string;
  personType: 'PF' | 'PJ';
  document: string;
  companyName: string;
  tradeName: string;
  companyDescription: string;
}

export default function ProfilePage() {
  const router = useRouter();
  const { user, userData, setUserData } = useAuthStore();
  const [loading, setLoading] = useState(false);
  const [uploadingPhoto, setUploadingPhoto] = useState(false);
  const [formData, setFormData] = useState<ProfileFormData>({
    name: '',
    phone: '',
    profilePhoto: '',
    personType: 'PF',
    document: '',
    companyName: '',
    tradeName: '',
    companyDescription: ''
  });

  useEffect(() => {
    if (!user) {
      router.push('/login');
      return;
    }

    if (userData) {
      setFormData({
        name: userData.name || '',
        phone: userData.phone || '',
        profilePhoto: userData.profilePhoto || '',
        personType: userData.personType || 'PF',
        document: userData.document || '',
        companyName: userData.companyName || '',
        tradeName: userData.tradeName || '',
        companyDescription: userData.companyDescription || ''
      });
    }
  }, [user, userData, router]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handlePhotoUpload = async (file: File) => {
    if (!file || !user) return;

    setUploadingPhoto(true);
    try {
      const fileName = `profiles/${user.uid}_${Date.now()}_${file.name}`;
      const storageRef = ref(storage, fileName);

      await uploadBytes(storageRef, file);
      const downloadURL = await getDownloadURL(storageRef);

      setFormData(prev => ({
        ...prev,
        profilePhoto: downloadURL
      }));

      toast.success('Foto carregada com sucesso!');
    } catch (error) {
      console.error('Erro ao fazer upload da foto:', error);
      toast.error('Erro ao carregar foto');
    } finally {
      setUploadingPhoto(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user || !userData) return;

    setLoading(true);

    try {
      await updateDoc(doc(db, 'users', user.uid), {
        ...formData,
        updatedAt: new Date()
      });

      // Atualizar estado local
      setUserData({
        ...userData,
        ...formData
      });

      toast.success('Perfil atualizado com sucesso!');
    } catch (error) {
      console.error('Erro ao atualizar perfil:', error);
      toast.error('Erro ao atualizar perfil');
    } finally {
      setLoading(false);
    }
  };

  if (!user || !userData) {
    return null;
  }

  return (
    <div className="min-h-screen bg-black text-white">
      <Header />

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 bg-clip-text text-transparent">
            Meu Perfil
          </h1>
          <p className="text-gray-400 mt-2">
            Gerencie suas informações pessoais e empresariais
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Foto de Perfil */}
          <Card className="bg-gray-900 border-gray-700">
            <CardHeader>
              <CardTitle className="flex items-center text-yellow-400">
                <Camera className="w-5 h-5 mr-2" />
                Foto de Perfil
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-6">
                <div className="relative">
                  {formData.profilePhoto ? (
                    <img
                      src={formData.profilePhoto}
                      alt="Foto de perfil"
                      className="w-24 h-24 rounded-full object-cover border-2 border-yellow-500"
                    />
                  ) : (
                    <div className="w-24 h-24 rounded-full bg-gray-700 flex items-center justify-center border-2 border-gray-600">
                      <User className="w-12 h-12 text-gray-400" />
                    </div>
                  )}
                  {uploadingPhoto && (
                    <div className="absolute inset-0 bg-black/50 rounded-full flex items-center justify-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-yellow-500"></div>
                    </div>
                  )}
                </div>

                <div>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => e.target.files?.[0] && handlePhotoUpload(e.target.files[0])}
                    className="hidden"
                    id="photo-upload"
                    disabled={uploadingPhoto}
                  />
                  <label
                    htmlFor="photo-upload"
                    className="cursor-pointer inline-flex items-center px-4 py-2 bg-yellow-500 text-black rounded-lg hover:bg-yellow-600 transition-colors"
                  >
                    <Camera className="w-4 h-4 mr-2" />
                    {uploadingPhoto ? 'Carregando...' : 'Alterar Foto'}
                  </label>
                  <p className="text-xs text-gray-400 mt-2">
                    JPG, PNG ou WebP. Máximo 5MB.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Informações Básicas */}
          <Card className="bg-gray-900 border-gray-700">
            <CardHeader>
              <CardTitle className="flex items-center text-yellow-400">
                <User className="w-5 h-5 mr-2" />
                Informações Básicas
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium mb-2">Nome Completo</label>
                  <Input
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    className="bg-gray-800 border-gray-700 text-white"
                    placeholder="Seu nome completo"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Telefone</label>
                  <Input
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    className="bg-gray-800 border-gray-700 text-white"
                    placeholder="(00) 00000-0000"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">E-mail</label>
                <Input
                  value={user?.email || ''}
                  className="bg-gray-700 border-gray-600 text-gray-400"
                  disabled
                />
                <p className="text-xs text-gray-500 mt-1">
                  O e-mail não pode ser alterado
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Tipo de Pessoa */}
          <Card className="bg-gray-900 border-gray-700">
            <CardHeader>
              <CardTitle className="flex items-center text-yellow-400">
                <Building className="w-5 h-5 mr-2" />
                Tipo de Empresa
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <label className="flex items-center space-x-3 cursor-pointer p-4 border border-gray-700 rounded-lg hover:border-yellow-500 transition-colors">
                  <input
                    type="radio"
                    name="personType"
                    value="PF"
                    checked={formData.personType === 'PF'}
                    onChange={handleChange}
                    className="text-yellow-500"
                  />
                  <div>
                    <div className="font-medium">Pessoa Física</div>
                    <div className="text-sm text-gray-400">Empresa Informal</div>
                  </div>
                </label>

                <label className="flex items-center space-x-3 cursor-pointer p-4 border border-gray-700 rounded-lg hover:border-yellow-500 transition-colors">
                  <input
                    type="radio"
                    name="personType"
                    value="PJ"
                    checked={formData.personType === 'PJ'}
                    onChange={handleChange}
                    className="text-yellow-500"
                  />
                  <div>
                    <div className="font-medium">Pessoa Jurídica</div>
                    <div className="text-sm text-gray-400">Empresa Formal</div>
                  </div>
                </label>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    {formData.personType === 'PJ' ? 'CNPJ' : 'CPF'}
                  </label>
                  <Input
                    name="document"
                    value={formData.document}
                    onChange={handleChange}
                    className="bg-gray-800 border-gray-700 text-white"
                    placeholder={formData.personType === 'PJ' ? '00.000.000/0000-00' : '000.000.000-00'}
                  />
                </div>

                {formData.personType === 'PJ' && (
                  <div>
                    <label className="block text-sm font-medium mb-2">Nome Fantasia</label>
                    <Input
                      name="tradeName"
                      value={formData.tradeName}
                      onChange={handleChange}
                      className="bg-gray-800 border-gray-700 text-white"
                      placeholder="Nome fantasia da empresa"
                    />
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Descrição da Empresa</label>
                <textarea
                  name="companyDescription"
                  value={formData.companyDescription}
                  onChange={handleChange}
                  className="w-full bg-gray-800 border border-gray-700 text-white rounded-md px-3 py-2 h-24 resize-none"
                  placeholder="Descreva brevemente sua empresa, produtos ou serviços..."
                />
              </div>
            </CardContent>
          </Card>

          {/* Botão de Salvar */}
          <div className="flex justify-end">
            <Button
              type="submit"
              disabled={loading}
              className="bg-gradient-to-r from-yellow-400 to-yellow-500 text-black hover:from-yellow-500 hover:to-yellow-600 px-8"
            >
              <Save className="w-4 h-4 mr-2" />
              {loading ? 'Salvando...' : 'Salvar Perfil'}
            </Button>
          </div>
        </form>
      </main>
    </div>
  );
}
