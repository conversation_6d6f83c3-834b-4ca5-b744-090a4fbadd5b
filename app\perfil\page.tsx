'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { doc, updateDoc } from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import {
  User,
  Mail,
  Phone,
  Save,
  MapPin,
  Building,
  Camera,
  Store,
  FileText,
  CreditCard,
  Users
} from 'lucide-react';
import { db, storage } from '@/src/lib/firebase';
import Header from '@/src/components/Header';
import { Button } from '@/src/components/ui/button';
import { Input } from '@/src/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/src/components/ui/card';
import { Badge } from '@/src/components/ui/badge';
import { useAuthStore } from '@/src/store/auth';
import { toast } from 'sonner';

interface ProfileFormData {
  name: string;
  phone: string;
  profilePhoto: string;

  // Tipo de pessoa
  personType: 'PF' | 'PJ';

  // Dados pessoais/empresariais
  document: string; // CPF ou CNPJ
  companyName?: string; // Para PJ
  tradeName?: string; // Nome fantasia para PJ

  // Endereço
  address: {
    street: string;
    number: string;
    complement: string;
    neighborhood: string;
    city: string;
    state: string;
    zipCode: string;
  };

  // Informações da empresa
  companyDescription: string;

  // Lojas/Pontos de venda
  stores: Array<{
    name: string;
    address: string;
    phone: string;
    type: string; // física, online, etc
  }>;
}

export default function ProfilePage() {
  const router = useRouter();
  const { user, userData, setUserData } = useAuthStore();
  const [loading, setLoading] = useState(false);
  const [uploadingPhoto, setUploadingPhoto] = useState(false);
  const [formData, setFormData] = useState<ProfileFormData>({
    name: '',
    phone: '',
    profilePhoto: '',
    personType: 'PF',
    document: '',
    companyName: '',
    tradeName: '',
    address: {
      street: '',
      number: '',
      complement: '',
      neighborhood: '',
      city: '',
      state: '',
      zipCode: ''
    },
    companyDescription: '',
    stores: []
  });

  useEffect(() => {
    if (!user) {
      router.push('/login');
      return;
    }

    if (userData) {
      setFormData({
        name: userData.name || '',
        phone: userData.phone || '',
        profilePhoto: userData.profilePhoto || '',
        personType: userData.personType || 'PF',
        document: userData.document || '',
        companyName: userData.companyName || '',
        tradeName: userData.tradeName || '',
        address: {
          street: userData.address?.street || '',
          number: userData.address?.number || '',
          complement: userData.address?.complement || '',
          neighborhood: userData.address?.neighborhood || '',
          city: userData.address?.city || '',
          state: userData.address?.state || '',
          zipCode: userData.address?.zipCode || ''
        },
        companyDescription: userData.companyDescription || '',
        stores: userData.stores || []
      });
    }
  }, [user, userData, router]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    if (name.startsWith('address.')) {
      const addressField = name.split('.')[1];
      setFormData(prev => ({
        ...prev,
        address: {
          ...prev.address,
          [addressField]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handlePhotoUpload = async (file: File) => {
    if (!file || !user) return;

    setUploadingPhoto(true);
    try {
      const fileName = `profiles/${user.uid}_${Date.now()}_${file.name}`;
      const storageRef = ref(storage, fileName);

      await uploadBytes(storageRef, file);
      const downloadURL = await getDownloadURL(storageRef);

      setFormData(prev => ({
        ...prev,
        profilePhoto: downloadURL
      }));

      toast.success('Foto carregada com sucesso!');
    } catch (error) {
      console.error('Erro ao fazer upload da foto:', error);
      toast.error('Erro ao carregar foto');
    } finally {
      setUploadingPhoto(false);
    }
  };

  const addStore = () => {
    setFormData(prev => ({
      ...prev,
      stores: [
        ...prev.stores,
        { name: '', address: '', phone: '', type: 'física' }
      ]
    }));
  };

  const updateStore = (index: number, field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      stores: prev.stores.map((store, i) =>
        i === index ? { ...store, [field]: value } : store
      )
    }));
  };

  const removeStore = (index: number) => {
    setFormData(prev => ({
      ...prev,
      stores: prev.stores.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user || !userData) return;

    setLoading(true);

    try {
      await updateDoc(doc(db, 'users', user.uid), {
        ...formData,
        updatedAt: new Date()
      });

      // Atualizar estado local
      setUserData({
        ...userData,
        ...formData
      });

      toast.success('Perfil atualizado com sucesso!');
    } catch (error) {
      console.error('Erro ao atualizar perfil:', error);
      toast.error('Erro ao atualizar perfil');
    } finally {
      setLoading(false);
    }
  };

  if (!user || !userData) {
    return null;
  }

  return (
    <div className="min-h-screen bg-black text-white">
      <Header />

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 bg-clip-text text-transparent">
            Meu Perfil
          </h1>
          <p className="text-gray-400 mt-2">
            Gerencie suas informações pessoais e empresariais
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Foto de Perfil */}
          <Card className="bg-gray-900 border-gray-700">
            <CardHeader>
              <CardTitle className="flex items-center text-yellow-400">
                <Camera className="w-5 h-5 mr-2" />
                Foto de Perfil
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-6">
                <div className="relative">
                  {formData.profilePhoto ? (
                    <img
                      src={formData.profilePhoto}
                      alt="Foto de perfil"
                      className="w-24 h-24 rounded-full object-cover border-2 border-yellow-500"
                    />
                  ) : (
                    <div className="w-24 h-24 rounded-full bg-gray-700 flex items-center justify-center border-2 border-gray-600">
                      <User className="w-12 h-12 text-gray-400" />
                    </div>
                  )}
                  {uploadingPhoto && (
                    <div className="absolute inset-0 bg-black/50 rounded-full flex items-center justify-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-yellow-500"></div>
                    </div>
                  )}
                </div>

                <div>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => e.target.files?.[0] && handlePhotoUpload(e.target.files[0])}
                    className="hidden"
                    id="photo-upload"
                    disabled={uploadingPhoto}
                  />
                  <label
                    htmlFor="photo-upload"
                    className="cursor-pointer inline-flex items-center px-4 py-2 bg-yellow-500 text-black rounded-lg hover:bg-yellow-600 transition-colors"
                  >
                    <Camera className="w-4 h-4 mr-2" />
                    {uploadingPhoto ? 'Carregando...' : 'Alterar Foto'}
                  </label>
                  <p className="text-xs text-gray-400 mt-2">
                    JPG, PNG ou WebP. Máximo 5MB.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tipo de Pessoa */}
          <Card className="bg-gray-900 border-gray-700">
            <CardHeader>
              <CardTitle className="flex items-center text-yellow-400">
                <CreditCard className="w-5 h-5 mr-2" />
                Tipo de Pessoa
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <label className="flex items-center space-x-3 cursor-pointer">
                  <input
                    type="radio"
                    name="personType"
                    value="PF"
                    checked={formData.personType === 'PF'}
                    onChange={handleChange}
                    className="text-yellow-500"
                  />
                  <div>
                    <div className="font-medium">Pessoa Física</div>
                    <div className="text-sm text-gray-400">Empresa Informal</div>
                  </div>
                </label>

                <label className="flex items-center space-x-3 cursor-pointer">
                  <input
                    type="radio"
                    name="personType"
                    value="PJ"
                    checked={formData.personType === 'PJ'}
                    onChange={handleChange}
                    className="text-yellow-500"
                  />
                  <div>
                    <div className="font-medium">Pessoa Jurídica</div>
                    <div className="text-sm text-gray-400">Empresa Formal</div>
                  </div>
                </label>
              </div>
            </CardContent>
          </Card>

          {/* Informações Pessoais/Empresariais */}
          <Card className="bg-gray-900 border-gray-700">
            <CardHeader>
              <CardTitle className="flex items-center text-yellow-400">
                <User className="w-5 h-5 mr-2" />
                {formData.personType === 'PJ' ? 'Informações da Empresa' : 'Informações Pessoais'}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    {formData.personType === 'PJ' ? 'Razão Social' : 'Nome Completo'}
                  </label>
                  <Input
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    className="bg-gray-800 border-gray-700 text-white"
                    placeholder={formData.personType === 'PJ' ? 'Razão social da empresa' : 'Seu nome completo'}
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    {formData.personType === 'PJ' ? 'CNPJ' : 'CPF'}
                  </label>
                  <Input
                    name="document"
                    value={formData.document}
                    onChange={handleChange}
                    className="bg-gray-800 border-gray-700 text-white"
                    placeholder={formData.personType === 'PJ' ? '00.000.000/0000-00' : '000.000.000-00'}
                  />
                </div>
              </div>

              {formData.personType === 'PJ' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium mb-2">Nome Fantasia</label>
                    <Input
                      name="tradeName"
                      value={formData.tradeName}
                      onChange={handleChange}
                      className="bg-gray-800 border-gray-700 text-white"
                      placeholder="Nome fantasia da empresa"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Telefone</label>
                    <Input
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      className="bg-gray-800 border-gray-700 text-white"
                      placeholder="(00) 00000-0000"
                    />
                  </div>
                </div>
              )}

              {formData.personType === 'PF' && (
                <div>
                  <label className="block text-sm font-medium mb-2">Telefone</label>
                  <Input
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    className="bg-gray-800 border-gray-700 text-white"
                    placeholder="(00) 00000-0000"
                  />
                </div>
              )}

              <div>
                <label className="block text-sm font-medium mb-2">E-mail</label>
                <Input
                  value={user?.email || ''}
                  className="bg-gray-700 border-gray-600 text-gray-400"
                  disabled
                />
                <p className="text-xs text-gray-500 mt-1">
                  O e-mail não pode ser alterado
                </p>
              </div>
            </CardContent>
          </Card>
                    id="name"
                    name="name"
                    type="text"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="pl-10"
                    placeholder="Seu nome completo"
                  />
                </div>
              </div>
              
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  E-mail
                </label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    id="email"
                    type="email"
                    value={userData.email}
                    disabled
                    className="pl-10 bg-gray-50"
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  O e-mail não pode ser alterado
                </p>
              </div>
              
              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                  Telefone
                </label>
                <div className="relative">
                  <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    id="phone"
                    name="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={handleChange}
                    className="pl-10"
                    placeholder="(11) 99999-9999"
                  />
                </div>
              </div>

              <div className="pt-4">
                <Button 
                  type="submit" 
                  className="w-full"
                  disabled={loading}
                >
                  <Save className="w-4 h-4 mr-2" />
                  {loading ? 'Salvando...' : 'Salvar Alterações'}
                </Button>
              </div>
            </CardContent>
          </form>
        </Card>

        {/* Informações da Conta */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Informações da Conta</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span className="text-gray-600">Tipo de Conta:</span>
              <span className="font-medium">
                {userData.role === 'admin' ? 'Administrador' : 'Cliente'}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Membro desde:</span>
              <span className="font-medium">
                {userData.createdAt?.toDate().toLocaleDateString('pt-BR')}
              </span>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
}
