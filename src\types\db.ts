import { Timestamp } from 'firebase/firestore';

export interface User {
  id: string;
  name: string;
  email: string;
  phone?: string;
  role: 'customer' | 'admin';
  createdAt: Timestamp;

  // Campos adicionais do perfil
  profilePhoto?: string;
  personType?: 'PF' | 'PJ';
  document?: string; // CPF ou CNPJ
  companyName?: string; // Para PJ
  tradeName?: string; // Nome fantasia para PJ

  // Endereço
  address?: {
    street: string;
    number: string;
    complement: string;
    neighborhood: string;
    city: string;
    state: string;
    zipCode: string;
  };

  // Informações da empresa
  companyDescription?: string;

  // Lojas/Pontos de venda
  stores?: Array<{
    name: string;
    address: string;
    phone: string;
    type: string; // física, online, etc
  }>;
}

export interface Address {
  id: string;
  userId: string;
  cep: string;
  street: string;
  number: string;
  complement?: string;
  district: string;
  city: string;
  state: string;
  isDefault?: boolean;
}

export interface Category {
  id: string;
  name: string;
  order: number;
}

export interface Product {
  id: string;
  sku: string;
  name: string;
  description: string;
  price: number;
  images: string[];
  categoryId: string;
  active: boolean;
  createdAt: Timestamp;
}

export interface CartItem {
  productId: string;
  name: string;
  price: number;
  qty: number;
  image: string;
}

export interface Cart {
  userId: string;
  items: CartItem[];
  totalGross: number;
  discountPercent: number;
  totalNet: number;
  updatedAt: Timestamp;
}

export interface PaymentInfo {
  method: 'PIX_FIXO' | 'DEPOSITO';
  amount: number;
  pixPayload?: string;
  bankInfo?: {
    bank: string;
    agency: string;
    account: string;
    name: string;
    doc: string;
  };
  proofFileUrl?: string;
}

export interface Order {
  id: string;
  userId: string;
  items: CartItem[];
  totals: {
    gross: number;
    discountPercent: number;
    net: number;
  };
  tierApplied: 0 | 10 | 15 | 20;
  addressId: string;
  payment: PaymentInfo;
  status: 'AWAITING_PROOF' | 'PROOF_SUBMITTED' | 'APPROVED' | 'REJECTED' | 'CANCELLED';
  createdAt: Timestamp;
  updatedAt?: Timestamp;
}

export interface Message {
  id: string;
  orderId: string;
  fromUserId: string;
  toUserId: string;
  text: string;
  read: boolean;
  createdAt: Timestamp;
}

export interface Notification {
  id: string;
  userId: string;
  type: 'NEW_ORDER' | 'MSG' | 'ORDER_STATUS';
  payload: any;
  read: boolean;
  createdAt: Timestamp;
}

export type OrderStatus = Order['status'];
export type UserRole = User['role'];
export type PaymentMethod = PaymentInfo['method'];
