"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@grpc+proto-loader@0.7.15";
exports.ids = ["vendor-chunks/@grpc+proto-loader@0.7.15"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@grpc+proto-loader@0.7.15/node_modules/@grpc/proto-loader/build/src/index.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@grpc+proto-loader@0.7.15/node_modules/@grpc/proto-loader/build/src/index.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/**\n * @license\n * Copyright 2018 gRPC authors.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.loadFileDescriptorSetFromObject = exports.loadFileDescriptorSetFromBuffer = exports.fromJSON = exports.loadSync = exports.load = exports.IdempotencyLevel = exports.isAnyExtension = exports.Long = void 0;\nconst camelCase = __webpack_require__(/*! lodash.camelcase */ \"(ssr)/./node_modules/.pnpm/lodash.camelcase@4.3.0/node_modules/lodash.camelcase/index.js\");\nconst Protobuf = __webpack_require__(/*! protobufjs */ \"(ssr)/./node_modules/.pnpm/protobufjs@7.5.4/node_modules/protobufjs/index.js\");\nconst descriptor = __webpack_require__(/*! protobufjs/ext/descriptor */ \"(ssr)/./node_modules/.pnpm/protobufjs@7.5.4/node_modules/protobufjs/ext/descriptor/index.js\");\nconst util_1 = __webpack_require__(/*! ./util */ \"(ssr)/./node_modules/.pnpm/@grpc+proto-loader@0.7.15/node_modules/@grpc/proto-loader/build/src/util.js\");\nconst Long = __webpack_require__(/*! long */ \"(ssr)/./node_modules/.pnpm/long@5.3.2/node_modules/long/umd/index.js\");\nexports.Long = Long;\nfunction isAnyExtension(obj) {\n    return ('@type' in obj) && (typeof obj['@type'] === 'string');\n}\nexports.isAnyExtension = isAnyExtension;\nvar IdempotencyLevel;\n(function (IdempotencyLevel) {\n    IdempotencyLevel[\"IDEMPOTENCY_UNKNOWN\"] = \"IDEMPOTENCY_UNKNOWN\";\n    IdempotencyLevel[\"NO_SIDE_EFFECTS\"] = \"NO_SIDE_EFFECTS\";\n    IdempotencyLevel[\"IDEMPOTENT\"] = \"IDEMPOTENT\";\n})(IdempotencyLevel = exports.IdempotencyLevel || (exports.IdempotencyLevel = {}));\nconst descriptorOptions = {\n    longs: String,\n    enums: String,\n    bytes: String,\n    defaults: true,\n    oneofs: true,\n    json: true,\n};\nfunction joinName(baseName, name) {\n    if (baseName === '') {\n        return name;\n    }\n    else {\n        return baseName + '.' + name;\n    }\n}\nfunction isHandledReflectionObject(obj) {\n    return (obj instanceof Protobuf.Service ||\n        obj instanceof Protobuf.Type ||\n        obj instanceof Protobuf.Enum);\n}\nfunction isNamespaceBase(obj) {\n    return obj instanceof Protobuf.Namespace || obj instanceof Protobuf.Root;\n}\nfunction getAllHandledReflectionObjects(obj, parentName) {\n    const objName = joinName(parentName, obj.name);\n    if (isHandledReflectionObject(obj)) {\n        return [[objName, obj]];\n    }\n    else {\n        if (isNamespaceBase(obj) && typeof obj.nested !== 'undefined') {\n            return Object.keys(obj.nested)\n                .map(name => {\n                return getAllHandledReflectionObjects(obj.nested[name], objName);\n            })\n                .reduce((accumulator, currentValue) => accumulator.concat(currentValue), []);\n        }\n    }\n    return [];\n}\nfunction createDeserializer(cls, options) {\n    return function deserialize(argBuf) {\n        return cls.toObject(cls.decode(argBuf), options);\n    };\n}\nfunction createSerializer(cls) {\n    return function serialize(arg) {\n        if (Array.isArray(arg)) {\n            throw new Error(`Failed to serialize message: expected object with ${cls.name} structure, got array instead`);\n        }\n        const message = cls.fromObject(arg);\n        return cls.encode(message).finish();\n    };\n}\nfunction mapMethodOptions(options) {\n    return (options || []).reduce((obj, item) => {\n        for (const [key, value] of Object.entries(item)) {\n            switch (key) {\n                case 'uninterpreted_option':\n                    obj.uninterpreted_option.push(item.uninterpreted_option);\n                    break;\n                default:\n                    obj[key] = value;\n            }\n        }\n        return obj;\n    }, {\n        deprecated: false,\n        idempotency_level: IdempotencyLevel.IDEMPOTENCY_UNKNOWN,\n        uninterpreted_option: [],\n    });\n}\nfunction createMethodDefinition(method, serviceName, options, fileDescriptors) {\n    /* This is only ever called after the corresponding root.resolveAll(), so we\n     * can assume that the resolved request and response types are non-null */\n    const requestType = method.resolvedRequestType;\n    const responseType = method.resolvedResponseType;\n    return {\n        path: '/' + serviceName + '/' + method.name,\n        requestStream: !!method.requestStream,\n        responseStream: !!method.responseStream,\n        requestSerialize: createSerializer(requestType),\n        requestDeserialize: createDeserializer(requestType, options),\n        responseSerialize: createSerializer(responseType),\n        responseDeserialize: createDeserializer(responseType, options),\n        // TODO(murgatroid99): Find a better way to handle this\n        originalName: camelCase(method.name),\n        requestType: createMessageDefinition(requestType, fileDescriptors),\n        responseType: createMessageDefinition(responseType, fileDescriptors),\n        options: mapMethodOptions(method.parsedOptions),\n    };\n}\nfunction createServiceDefinition(service, name, options, fileDescriptors) {\n    const def = {};\n    for (const method of service.methodsArray) {\n        def[method.name] = createMethodDefinition(method, name, options, fileDescriptors);\n    }\n    return def;\n}\nfunction createMessageDefinition(message, fileDescriptors) {\n    const messageDescriptor = message.toDescriptor('proto3');\n    return {\n        format: 'Protocol Buffer 3 DescriptorProto',\n        type: messageDescriptor.$type.toObject(messageDescriptor, descriptorOptions),\n        fileDescriptorProtos: fileDescriptors,\n    };\n}\nfunction createEnumDefinition(enumType, fileDescriptors) {\n    const enumDescriptor = enumType.toDescriptor('proto3');\n    return {\n        format: 'Protocol Buffer 3 EnumDescriptorProto',\n        type: enumDescriptor.$type.toObject(enumDescriptor, descriptorOptions),\n        fileDescriptorProtos: fileDescriptors,\n    };\n}\n/**\n * function createDefinition(obj: Protobuf.Service, name: string, options:\n * Options): ServiceDefinition; function createDefinition(obj: Protobuf.Type,\n * name: string, options: Options): MessageTypeDefinition; function\n * createDefinition(obj: Protobuf.Enum, name: string, options: Options):\n * EnumTypeDefinition;\n */\nfunction createDefinition(obj, name, options, fileDescriptors) {\n    if (obj instanceof Protobuf.Service) {\n        return createServiceDefinition(obj, name, options, fileDescriptors);\n    }\n    else if (obj instanceof Protobuf.Type) {\n        return createMessageDefinition(obj, fileDescriptors);\n    }\n    else if (obj instanceof Protobuf.Enum) {\n        return createEnumDefinition(obj, fileDescriptors);\n    }\n    else {\n        throw new Error('Type mismatch in reflection object handling');\n    }\n}\nfunction createPackageDefinition(root, options) {\n    const def = {};\n    root.resolveAll();\n    const descriptorList = root.toDescriptor('proto3').file;\n    const bufferList = descriptorList.map(value => Buffer.from(descriptor.FileDescriptorProto.encode(value).finish()));\n    for (const [name, obj] of getAllHandledReflectionObjects(root, '')) {\n        def[name] = createDefinition(obj, name, options, bufferList);\n    }\n    return def;\n}\nfunction createPackageDefinitionFromDescriptorSet(decodedDescriptorSet, options) {\n    options = options || {};\n    const root = Protobuf.Root.fromDescriptor(decodedDescriptorSet);\n    root.resolveAll();\n    return createPackageDefinition(root, options);\n}\n/**\n * Load a .proto file with the specified options.\n * @param filename One or multiple file paths to load. Can be an absolute path\n *     or relative to an include path.\n * @param options.keepCase Preserve field names. The default is to change them\n *     to camel case.\n * @param options.longs The type that should be used to represent `long` values.\n *     Valid options are `Number` and `String`. Defaults to a `Long` object type\n *     from a library.\n * @param options.enums The type that should be used to represent `enum` values.\n *     The only valid option is `String`. Defaults to the numeric value.\n * @param options.bytes The type that should be used to represent `bytes`\n *     values. Valid options are `Array` and `String`. The default is to use\n *     `Buffer`.\n * @param options.defaults Set default values on output objects. Defaults to\n *     `false`.\n * @param options.arrays Set empty arrays for missing array values even if\n *     `defaults` is `false`. Defaults to `false`.\n * @param options.objects Set empty objects for missing object values even if\n *     `defaults` is `false`. Defaults to `false`.\n * @param options.oneofs Set virtual oneof properties to the present field's\n *     name\n * @param options.json Represent Infinity and NaN as strings in float fields,\n *     and automatically decode google.protobuf.Any values.\n * @param options.includeDirs Paths to search for imported `.proto` files.\n */\nfunction load(filename, options) {\n    return (0, util_1.loadProtosWithOptions)(filename, options).then(loadedRoot => {\n        return createPackageDefinition(loadedRoot, options);\n    });\n}\nexports.load = load;\nfunction loadSync(filename, options) {\n    const loadedRoot = (0, util_1.loadProtosWithOptionsSync)(filename, options);\n    return createPackageDefinition(loadedRoot, options);\n}\nexports.loadSync = loadSync;\nfunction fromJSON(json, options) {\n    options = options || {};\n    const loadedRoot = Protobuf.Root.fromJSON(json);\n    loadedRoot.resolveAll();\n    return createPackageDefinition(loadedRoot, options);\n}\nexports.fromJSON = fromJSON;\nfunction loadFileDescriptorSetFromBuffer(descriptorSet, options) {\n    const decodedDescriptorSet = descriptor.FileDescriptorSet.decode(descriptorSet);\n    return createPackageDefinitionFromDescriptorSet(decodedDescriptorSet, options);\n}\nexports.loadFileDescriptorSetFromBuffer = loadFileDescriptorSetFromBuffer;\nfunction loadFileDescriptorSetFromObject(descriptorSet, options) {\n    const decodedDescriptorSet = descriptor.FileDescriptorSet.fromObject(descriptorSet);\n    return createPackageDefinitionFromDescriptorSet(decodedDescriptorSet, options);\n}\nexports.loadFileDescriptorSetFromObject = loadFileDescriptorSetFromObject;\n(0, util_1.addCommonProtos)();\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@grpc+proto-loader@0.7.15/node_modules/@grpc/proto-loader/build/src/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@grpc+proto-loader@0.7.15/node_modules/@grpc/proto-loader/build/src/util.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@grpc+proto-loader@0.7.15/node_modules/@grpc/proto-loader/build/src/util.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/**\n * @license\n * Copyright 2018 gRPC authors.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n *\n */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.addCommonProtos = exports.loadProtosWithOptionsSync = exports.loadProtosWithOptions = void 0;\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst path = __webpack_require__(/*! path */ \"path\");\nconst Protobuf = __webpack_require__(/*! protobufjs */ \"(ssr)/./node_modules/.pnpm/protobufjs@7.5.4/node_modules/protobufjs/index.js\");\nfunction addIncludePathResolver(root, includePaths) {\n    const originalResolvePath = root.resolvePath;\n    root.resolvePath = (origin, target) => {\n        if (path.isAbsolute(target)) {\n            return target;\n        }\n        for (const directory of includePaths) {\n            const fullPath = path.join(directory, target);\n            try {\n                fs.accessSync(fullPath, fs.constants.R_OK);\n                return fullPath;\n            }\n            catch (err) {\n                continue;\n            }\n        }\n        process.emitWarning(`${target} not found in any of the include paths ${includePaths}`);\n        return originalResolvePath(origin, target);\n    };\n}\nasync function loadProtosWithOptions(filename, options) {\n    const root = new Protobuf.Root();\n    options = options || {};\n    if (!!options.includeDirs) {\n        if (!Array.isArray(options.includeDirs)) {\n            return Promise.reject(new Error('The includeDirs option must be an array'));\n        }\n        addIncludePathResolver(root, options.includeDirs);\n    }\n    const loadedRoot = await root.load(filename, options);\n    loadedRoot.resolveAll();\n    return loadedRoot;\n}\nexports.loadProtosWithOptions = loadProtosWithOptions;\nfunction loadProtosWithOptionsSync(filename, options) {\n    const root = new Protobuf.Root();\n    options = options || {};\n    if (!!options.includeDirs) {\n        if (!Array.isArray(options.includeDirs)) {\n            throw new Error('The includeDirs option must be an array');\n        }\n        addIncludePathResolver(root, options.includeDirs);\n    }\n    const loadedRoot = root.loadSync(filename, options);\n    loadedRoot.resolveAll();\n    return loadedRoot;\n}\nexports.loadProtosWithOptionsSync = loadProtosWithOptionsSync;\n/**\n * Load Google's well-known proto files that aren't exposed by Protobuf.js.\n */\nfunction addCommonProtos() {\n    // Protobuf.js exposes: any, duration, empty, field_mask, struct, timestamp,\n    // and wrappers. compiler/plugin is excluded in Protobuf.js and here.\n    // Using constant strings for compatibility with tools like Webpack\n    const apiDescriptor = __webpack_require__(/*! protobufjs/google/protobuf/api.json */ \"(ssr)/./node_modules/.pnpm/protobufjs@7.5.4/node_modules/protobufjs/google/protobuf/api.json\");\n    const descriptorDescriptor = __webpack_require__(/*! protobufjs/google/protobuf/descriptor.json */ \"(ssr)/./node_modules/.pnpm/protobufjs@7.5.4/node_modules/protobufjs/google/protobuf/descriptor.json\");\n    const sourceContextDescriptor = __webpack_require__(/*! protobufjs/google/protobuf/source_context.json */ \"(ssr)/./node_modules/.pnpm/protobufjs@7.5.4/node_modules/protobufjs/google/protobuf/source_context.json\");\n    const typeDescriptor = __webpack_require__(/*! protobufjs/google/protobuf/type.json */ \"(ssr)/./node_modules/.pnpm/protobufjs@7.5.4/node_modules/protobufjs/google/protobuf/type.json\");\n    Protobuf.common('api', apiDescriptor.nested.google.nested.protobuf.nested);\n    Protobuf.common('descriptor', descriptorDescriptor.nested.google.nested.protobuf.nested);\n    Protobuf.common('source_context', sourceContextDescriptor.nested.google.nested.protobuf.nested);\n    Protobuf.common('type', typeDescriptor.nested.google.nested.protobuf.nested);\n}\nexports.addCommonProtos = addCommonProtos;\n//# sourceMappingURL=util.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@grpc+proto-loader@0.7.15/node_modules/@grpc/proto-loader/build/src/util.js\n");

/***/ })

};
;