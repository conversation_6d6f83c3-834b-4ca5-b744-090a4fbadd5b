'use client';

import Image from 'next/image';
import Link from 'next/link';
import { ShoppingBag } from 'lucide-react';
import { Button } from '@/src/components/ui/button';
import { Card, CardContent, CardFooter } from '@/src/components/ui/card';
import { Product } from '@/src/types/db';
import { formatCurrency } from '@/src/lib/discount';
import { useCartStore } from '@/src/store/cart';
import { useAuthStore } from '@/src/store/auth';
import { toast } from 'sonner';

interface ProductCardProps {
  product: Product;
}

export function ProductCard({ product }: ProductCardProps) {
  const { addItem } = useCartStore();
  const { user } = useAuthStore();

  const handleAddToBag = () => {
    if (!user) {
      toast.error('Faça login para adicionar produtos à mala de compras');
      return;
    }

    addItem({
      productId: product.id,
      name: product.name,
      price: product.price,
      qty: 1,
      image: product.images[0] || '/placeholder-product.jpg'
    });

    toast.success('Produto adicionado à mala de compras!');
  };

  return (
    <Card className="group hover:shadow-lg transition-shadow duration-200">
      <CardContent className="p-0">
        <Link href={`/produto/${product.id}`}>
          <div className="relative aspect-square overflow-hidden rounded-t-lg">
            <Image
              src={product.images[0] || '/placeholder-product.jpg'}
              alt={product.name}
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-200"
            />
          </div>
        </Link>
        
        <div className="p-4">
          <Link href={`/produto/${product.id}`}>
            <h3 className="font-semibold text-lg mb-2 line-clamp-2 hover:text-blue-600 transition-colors">
              {product.name}
            </h3>
          </Link>
          
          <p className="text-gray-600 text-sm mb-3 line-clamp-2">
            {product.description}
          </p>
          
          <div className="flex items-center justify-between">
            <span className="text-2xl font-bold text-green-600">
              {formatCurrency(product.price)}
            </span>
            <span className="text-xs text-gray-500">
              SKU: {product.sku}
            </span>
          </div>
        </div>
      </CardContent>
      
      <CardFooter className="p-4 pt-0">
        <Button 
          onClick={handleAddToCart}
          className="w-full"
          disabled={!user}
        >
          <ShoppingCart className="w-4 h-4 mr-2" />
          {user ? 'Adicionar ao Carrinho' : 'Faça Login'}
        </Button>
      </CardFooter>
    </Card>
  );
}
