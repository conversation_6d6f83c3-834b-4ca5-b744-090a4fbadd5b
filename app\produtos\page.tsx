﻿'use client';

export default function ProdutosPage() {
  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f9fafb' }}>
      <header style={{
        backgroundColor: 'white',
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
        borderBottom: '1px solid #e5e7eb',
        padding: '0'
      }}>
        <div style={{
          maxWidth: '1200px',
          margin: '0 auto',
          padding: '0 1rem',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          height: '64px'
        }}>
          <div style={{
            fontSize: '1.5rem',
            fontWeight: 'bold',
            color: '#1f2937'
          }}>
            doCorpo
          </div>
          
          <nav style={{ display: 'flex', gap: '2rem' }}>
            <a href="/" style={{ color: '#6b7280', textDecoration: 'none' }}>Início</a>
            <a href="/produtos" style={{ color: '#6b7280', textDecoration: 'none' }}>Produtos</a>
            <a href="/carrinho" style={{ color: '#6b7280', textDecoration: 'none' }}>Carrinho</a>
            <a href="/login" style={{ color: '#6b7280', textDecoration: 'none' }}>Login</a>
          </nav>
        </div>
      </header>
      
      <main style={{ maxWidth: '1200px', margin: '0 auto', padding: '2rem 1rem' }}>
        <h1 style={{ fontSize: '2.5rem', fontWeight: 'bold', marginBottom: '2rem', color: '#1f2937' }}>
          Produtos Premium
        </h1>
        
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '2rem'
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '0.5rem',
            boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
            padding: '1.5rem'
          }}>
            <div style={{
              aspectRatio: '1',
              backgroundColor: '#e5e7eb',
              borderRadius: '0.5rem',
              marginBottom: '1rem',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#6b7280'
            }}>
              Imagem do Produto
            </div>
            <h3 style={{ fontSize: '1.25rem', fontWeight: '600', marginBottom: '0.5rem' }}>
              Produto Premium 1
            </h3>
            <p style={{ color: '#6b7280', marginBottom: '1rem' }}>
              Descrição do produto premium de alta qualidade
            </p>
            <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#059669', marginBottom: '1rem' }}>
              R$ 99,90
            </div>
            <button style={{
              width: '100%',
              padding: '0.75rem',
              backgroundColor: '#2563eb',
              color: 'white',
              border: 'none',
              borderRadius: '0.5rem',
              cursor: 'pointer'
            }}>
              Adicionar ao Carrinho
            </button>
          </div>
        </div>
      </main>
    </div>
  );
}
