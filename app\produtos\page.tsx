﻿'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { collection, getDocs, query, where, orderBy } from 'firebase/firestore';
import { db } from '@/src/lib/firebase';
import { Header } from '@/src/components/Header';
import { ProductCard } from '@/src/components/ProductCard';
import { CategoryChips } from '@/src/components/CategoryChips';
import { DiscountGauge } from '@/src/components/DiscountGauge';
import { Product, Category } from '@/src/types/db';
import { useAuthStore } from '@/src/store/auth';

export default function ProdutosPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const { user } = useAuthStore();
  const router = useRouter();

  // Proteção de rota - redireciona para login se não estiver autenticado
  useEffect(() => {
    if (!user) {
      router.push('/login');
      return;
    }
  }, [user, router]);

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [user]);

  const loadData = async () => {
    try {
      // Carregar categorias
      const categoriesQuery = query(
        collection(db, 'categories'),
        orderBy('order', 'asc')
      );
      const categoriesSnapshot = await getDocs(categoriesQuery);
      const categoriesData = categoriesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Category[];

      // Carregar produtos ativos
      const productsQuery = query(
        collection(db, 'products'),
        where('active', '==', true),
        orderBy('createdAt', 'desc')
      );
      const productsSnapshot = await getDocs(productsQuery);
      const productsData = productsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Product[];

      setCategories(categoriesData);
      setProducts(productsData);
    } catch (error) {
      console.error('Erro ao carregar dados:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredProducts = selectedCategory
    ? products.filter(product => product.categoryId === selectedCategory)
    : products;

  // Se não estiver logado, não renderiza nada (será redirecionado)
  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-gray-100 to-gray-200">
      <Header />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Banner de Campanha VIP */}
        <div className="bg-gradient-to-r from-black via-gray-900 to-black text-white p-8 rounded-lg mb-8 border border-yellow-500/20 shadow-2xl">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 bg-clip-text text-transparent">
              Produtos Premium doCorpo
            </h1>
            <p className="text-xl mb-6 text-gray-100">
              Produtos premium exclusivos com descontos progressivos de até 20%
            </p>
            <div className="text-sm text-yellow-300/90 font-medium">
              ✨ Compre mais e economize mais: 10% a partir de R$ 1.000 | 15% a partir de R$ 2.000 | 20% a partir de R$ 3.000 ✨
            </div>
          </div>
        </div>

        {/* Gauge de Desconto */}
        <div className="mb-8">
          <DiscountGauge />
        </div>

        {/* Filtros de Categoria */}
        <div className="mb-8">
          <CategoryChips
            categories={categories}
            selectedCategory={selectedCategory}
            onSelectCategory={setSelectedCategory}
          />
        </div>

        {/* Grid de Produtos */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg p-4 animate-pulse">
                <div className="bg-gray-200 h-48 rounded mb-4"></div>
                <div className="bg-gray-200 h-4 rounded mb-2"></div>
                <div className="bg-gray-200 h-4 rounded w-2/3"></div>
              </div>
            ))}
          </div>
        ) : filteredProducts.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredProducts.map(product => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">
              {selectedCategory ? 'Nenhum produto encontrado nesta categoria.' : 'Nenhum produto disponível.'}
            </p>
          </div>
        )}
      </main>
    </div>
  );
}
