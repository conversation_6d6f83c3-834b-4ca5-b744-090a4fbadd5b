"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@firebase+storage@0.13.2_@firebase+app@0.10.13";
exports.ids = ["vendor-chunks/@firebase+storage@0.13.2_@firebase+app@0.10.13"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@firebase+storage@0.13.2_@firebase+app@0.10.13/node_modules/@firebase/storage/dist/node-esm/index.node.esm.js":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@firebase+storage@0.13.2_@firebase+app@0.10.13/node_modules/@firebase/storage/dist/node-esm/index.node.esm.js ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StorageError: () => (/* binding */ StorageError),\n/* harmony export */   StorageErrorCode: () => (/* binding */ StorageErrorCode),\n/* harmony export */   StringFormat: () => (/* binding */ StringFormat),\n/* harmony export */   _FbsBlob: () => (/* binding */ FbsBlob),\n/* harmony export */   _Location: () => (/* binding */ Location),\n/* harmony export */   _TaskEvent: () => (/* binding */ TaskEvent),\n/* harmony export */   _TaskState: () => (/* binding */ TaskState),\n/* harmony export */   _UploadTask: () => (/* binding */ UploadTask),\n/* harmony export */   _dataFromString: () => (/* binding */ dataFromString),\n/* harmony export */   _getChild: () => (/* binding */ _getChild),\n/* harmony export */   _invalidArgument: () => (/* binding */ invalidArgument),\n/* harmony export */   _invalidRootOperation: () => (/* binding */ invalidRootOperation),\n/* harmony export */   connectStorageEmulator: () => (/* binding */ connectStorageEmulator),\n/* harmony export */   deleteObject: () => (/* binding */ deleteObject),\n/* harmony export */   getBlob: () => (/* binding */ getBlob),\n/* harmony export */   getBytes: () => (/* binding */ getBytes),\n/* harmony export */   getDownloadURL: () => (/* binding */ getDownloadURL),\n/* harmony export */   getMetadata: () => (/* binding */ getMetadata),\n/* harmony export */   getStorage: () => (/* binding */ getStorage),\n/* harmony export */   getStream: () => (/* binding */ getStream),\n/* harmony export */   list: () => (/* binding */ list),\n/* harmony export */   listAll: () => (/* binding */ listAll),\n/* harmony export */   ref: () => (/* binding */ ref),\n/* harmony export */   updateMetadata: () => (/* binding */ updateMetadata),\n/* harmony export */   uploadBytes: () => (/* binding */ uploadBytes),\n/* harmony export */   uploadBytesResumable: () => (/* binding */ uploadBytesResumable),\n/* harmony export */   uploadString: () => (/* binding */ uploadString)\n/* harmony export */ });\n/* harmony import */ var _firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @firebase/app */ \"(ssr)/./node_modules/.pnpm/@firebase+app@0.10.13/node_modules/@firebase/app/dist/esm/index.esm2017.js\");\n/* harmony import */ var _firebase_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @firebase/util */ \"(ssr)/./node_modules/.pnpm/@firebase+util@1.10.0/node_modules/@firebase/util/dist/node-esm/index.node.esm.js\");\n/* harmony import */ var undici__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! undici */ \"(ssr)/./node_modules/.pnpm/undici@6.19.7/node_modules/undici/index.js\");\n/* harmony import */ var _firebase_component__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @firebase/component */ \"(ssr)/./node_modules/.pnpm/@firebase+component@0.6.9/node_modules/@firebase/component/dist/esm/index.esm2017.js\");\n\n\n\n\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * @fileoverview Constants used in the Firebase Storage library.\r\n */\r\n/**\r\n * Domain name for firebase storage.\r\n */\r\nconst DEFAULT_HOST = 'firebasestorage.googleapis.com';\r\n/**\r\n * The key in Firebase config json for the storage bucket.\r\n */\r\nconst CONFIG_STORAGE_BUCKET_KEY = 'storageBucket';\r\n/**\r\n * 2 minutes\r\n *\r\n * The timeout for all operations except upload.\r\n */\r\nconst DEFAULT_MAX_OPERATION_RETRY_TIME = 2 * 60 * 1000;\r\n/**\r\n * 10 minutes\r\n *\r\n * The timeout for upload.\r\n */\r\nconst DEFAULT_MAX_UPLOAD_RETRY_TIME = 10 * 60 * 1000;\r\n/**\r\n * 1 second\r\n */\r\nconst DEFAULT_MIN_SLEEP_TIME_MILLIS = 1000;\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * An error returned by the Firebase Storage SDK.\r\n * @public\r\n */\r\nclass StorageError extends _firebase_util__WEBPACK_IMPORTED_MODULE_1__.FirebaseError {\r\n    /**\r\n     * @param code - A `StorageErrorCode` string to be prefixed with 'storage/' and\r\n     *  added to the end of the message.\r\n     * @param message  - Error message.\r\n     * @param status_ - Corresponding HTTP Status Code\r\n     */\r\n    constructor(code, message, status_ = 0) {\r\n        super(prependCode(code), `Firebase Storage: ${message} (${prependCode(code)})`);\r\n        this.status_ = status_;\r\n        /**\r\n         * Stores custom error data unique to the `StorageError`.\r\n         */\r\n        this.customData = { serverResponse: null };\r\n        this._baseMessage = this.message;\r\n        // Without this, `instanceof StorageError`, in tests for example,\r\n        // returns false.\r\n        Object.setPrototypeOf(this, StorageError.prototype);\r\n    }\r\n    get status() {\r\n        return this.status_;\r\n    }\r\n    set status(status) {\r\n        this.status_ = status;\r\n    }\r\n    /**\r\n     * Compares a `StorageErrorCode` against this error's code, filtering out the prefix.\r\n     */\r\n    _codeEquals(code) {\r\n        return prependCode(code) === this.code;\r\n    }\r\n    /**\r\n     * Optional response message that was added by the server.\r\n     */\r\n    get serverResponse() {\r\n        return this.customData.serverResponse;\r\n    }\r\n    set serverResponse(serverResponse) {\r\n        this.customData.serverResponse = serverResponse;\r\n        if (this.customData.serverResponse) {\r\n            this.message = `${this._baseMessage}\\n${this.customData.serverResponse}`;\r\n        }\r\n        else {\r\n            this.message = this._baseMessage;\r\n        }\r\n    }\r\n}\r\n/**\r\n * @public\r\n * Error codes that can be attached to `StorageError` objects.\r\n */\r\nvar StorageErrorCode;\r\n(function (StorageErrorCode) {\r\n    // Shared between all platforms\r\n    StorageErrorCode[\"UNKNOWN\"] = \"unknown\";\r\n    StorageErrorCode[\"OBJECT_NOT_FOUND\"] = \"object-not-found\";\r\n    StorageErrorCode[\"BUCKET_NOT_FOUND\"] = \"bucket-not-found\";\r\n    StorageErrorCode[\"PROJECT_NOT_FOUND\"] = \"project-not-found\";\r\n    StorageErrorCode[\"QUOTA_EXCEEDED\"] = \"quota-exceeded\";\r\n    StorageErrorCode[\"UNAUTHENTICATED\"] = \"unauthenticated\";\r\n    StorageErrorCode[\"UNAUTHORIZED\"] = \"unauthorized\";\r\n    StorageErrorCode[\"UNAUTHORIZED_APP\"] = \"unauthorized-app\";\r\n    StorageErrorCode[\"RETRY_LIMIT_EXCEEDED\"] = \"retry-limit-exceeded\";\r\n    StorageErrorCode[\"INVALID_CHECKSUM\"] = \"invalid-checksum\";\r\n    StorageErrorCode[\"CANCELED\"] = \"canceled\";\r\n    // JS specific\r\n    StorageErrorCode[\"INVALID_EVENT_NAME\"] = \"invalid-event-name\";\r\n    StorageErrorCode[\"INVALID_URL\"] = \"invalid-url\";\r\n    StorageErrorCode[\"INVALID_DEFAULT_BUCKET\"] = \"invalid-default-bucket\";\r\n    StorageErrorCode[\"NO_DEFAULT_BUCKET\"] = \"no-default-bucket\";\r\n    StorageErrorCode[\"CANNOT_SLICE_BLOB\"] = \"cannot-slice-blob\";\r\n    StorageErrorCode[\"SERVER_FILE_WRONG_SIZE\"] = \"server-file-wrong-size\";\r\n    StorageErrorCode[\"NO_DOWNLOAD_URL\"] = \"no-download-url\";\r\n    StorageErrorCode[\"INVALID_ARGUMENT\"] = \"invalid-argument\";\r\n    StorageErrorCode[\"INVALID_ARGUMENT_COUNT\"] = \"invalid-argument-count\";\r\n    StorageErrorCode[\"APP_DELETED\"] = \"app-deleted\";\r\n    StorageErrorCode[\"INVALID_ROOT_OPERATION\"] = \"invalid-root-operation\";\r\n    StorageErrorCode[\"INVALID_FORMAT\"] = \"invalid-format\";\r\n    StorageErrorCode[\"INTERNAL_ERROR\"] = \"internal-error\";\r\n    StorageErrorCode[\"UNSUPPORTED_ENVIRONMENT\"] = \"unsupported-environment\";\r\n})(StorageErrorCode || (StorageErrorCode = {}));\r\nfunction prependCode(code) {\r\n    return 'storage/' + code;\r\n}\r\nfunction unknown() {\r\n    const message = 'An unknown error occurred, please check the error payload for ' +\r\n        'server response.';\r\n    return new StorageError(StorageErrorCode.UNKNOWN, message);\r\n}\r\nfunction objectNotFound(path) {\r\n    return new StorageError(StorageErrorCode.OBJECT_NOT_FOUND, \"Object '\" + path + \"' does not exist.\");\r\n}\r\nfunction quotaExceeded(bucket) {\r\n    return new StorageError(StorageErrorCode.QUOTA_EXCEEDED, \"Quota for bucket '\" +\r\n        bucket +\r\n        \"' exceeded, please view quota on \" +\r\n        'https://firebase.google.com/pricing/.');\r\n}\r\nfunction unauthenticated() {\r\n    const message = 'User is not authenticated, please authenticate using Firebase ' +\r\n        'Authentication and try again.';\r\n    return new StorageError(StorageErrorCode.UNAUTHENTICATED, message);\r\n}\r\nfunction unauthorizedApp() {\r\n    return new StorageError(StorageErrorCode.UNAUTHORIZED_APP, 'This app does not have permission to access Firebase Storage on this project.');\r\n}\r\nfunction unauthorized(path) {\r\n    return new StorageError(StorageErrorCode.UNAUTHORIZED, \"User does not have permission to access '\" + path + \"'.\");\r\n}\r\nfunction retryLimitExceeded() {\r\n    return new StorageError(StorageErrorCode.RETRY_LIMIT_EXCEEDED, 'Max retry time for operation exceeded, please try again.');\r\n}\r\nfunction canceled() {\r\n    return new StorageError(StorageErrorCode.CANCELED, 'User canceled the upload/download.');\r\n}\r\nfunction invalidUrl(url) {\r\n    return new StorageError(StorageErrorCode.INVALID_URL, \"Invalid URL '\" + url + \"'.\");\r\n}\r\nfunction invalidDefaultBucket(bucket) {\r\n    return new StorageError(StorageErrorCode.INVALID_DEFAULT_BUCKET, \"Invalid default bucket '\" + bucket + \"'.\");\r\n}\r\nfunction noDefaultBucket() {\r\n    return new StorageError(StorageErrorCode.NO_DEFAULT_BUCKET, 'No default bucket ' +\r\n        \"found. Did you set the '\" +\r\n        CONFIG_STORAGE_BUCKET_KEY +\r\n        \"' property when initializing the app?\");\r\n}\r\nfunction cannotSliceBlob() {\r\n    return new StorageError(StorageErrorCode.CANNOT_SLICE_BLOB, 'Cannot slice blob for upload. Please retry the upload.');\r\n}\r\nfunction serverFileWrongSize() {\r\n    return new StorageError(StorageErrorCode.SERVER_FILE_WRONG_SIZE, 'Server recorded incorrect upload file size, please retry the upload.');\r\n}\r\nfunction noDownloadURL() {\r\n    return new StorageError(StorageErrorCode.NO_DOWNLOAD_URL, 'The given file does not have any download URLs.');\r\n}\r\n/**\r\n * @internal\r\n */\r\nfunction invalidArgument(message) {\r\n    return new StorageError(StorageErrorCode.INVALID_ARGUMENT, message);\r\n}\r\nfunction appDeleted() {\r\n    return new StorageError(StorageErrorCode.APP_DELETED, 'The Firebase app was deleted.');\r\n}\r\n/**\r\n * @param name - The name of the operation that was invalid.\r\n *\r\n * @internal\r\n */\r\nfunction invalidRootOperation(name) {\r\n    return new StorageError(StorageErrorCode.INVALID_ROOT_OPERATION, \"The operation '\" +\r\n        name +\r\n        \"' cannot be performed on a root reference, create a non-root \" +\r\n        \"reference using child, such as .child('file.png').\");\r\n}\r\n/**\r\n * @param format - The format that was not valid.\r\n * @param message - A message describing the format violation.\r\n */\r\nfunction invalidFormat(format, message) {\r\n    return new StorageError(StorageErrorCode.INVALID_FORMAT, \"String does not match format '\" + format + \"': \" + message);\r\n}\r\n/**\r\n * @param message - A message describing the internal error.\r\n */\r\nfunction internalError(message) {\r\n    throw new StorageError(StorageErrorCode.INTERNAL_ERROR, 'Internal error: ' + message);\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Firebase Storage location data.\r\n *\r\n * @internal\r\n */\r\nclass Location {\r\n    constructor(bucket, path) {\r\n        this.bucket = bucket;\r\n        this.path_ = path;\r\n    }\r\n    get path() {\r\n        return this.path_;\r\n    }\r\n    get isRoot() {\r\n        return this.path.length === 0;\r\n    }\r\n    fullServerUrl() {\r\n        const encode = encodeURIComponent;\r\n        return '/b/' + encode(this.bucket) + '/o/' + encode(this.path);\r\n    }\r\n    bucketOnlyServerUrl() {\r\n        const encode = encodeURIComponent;\r\n        return '/b/' + encode(this.bucket) + '/o';\r\n    }\r\n    static makeFromBucketSpec(bucketString, host) {\r\n        let bucketLocation;\r\n        try {\r\n            bucketLocation = Location.makeFromUrl(bucketString, host);\r\n        }\r\n        catch (e) {\r\n            // Not valid URL, use as-is. This lets you put bare bucket names in\r\n            // config.\r\n            return new Location(bucketString, '');\r\n        }\r\n        if (bucketLocation.path === '') {\r\n            return bucketLocation;\r\n        }\r\n        else {\r\n            throw invalidDefaultBucket(bucketString);\r\n        }\r\n    }\r\n    static makeFromUrl(url, host) {\r\n        let location = null;\r\n        const bucketDomain = '([A-Za-z0-9.\\\\-_]+)';\r\n        function gsModify(loc) {\r\n            if (loc.path.charAt(loc.path.length - 1) === '/') {\r\n                loc.path_ = loc.path_.slice(0, -1);\r\n            }\r\n        }\r\n        const gsPath = '(/(.*))?$';\r\n        const gsRegex = new RegExp('^gs://' + bucketDomain + gsPath, 'i');\r\n        const gsIndices = { bucket: 1, path: 3 };\r\n        function httpModify(loc) {\r\n            loc.path_ = decodeURIComponent(loc.path);\r\n        }\r\n        const version = 'v[A-Za-z0-9_]+';\r\n        const firebaseStorageHost = host.replace(/[.]/g, '\\\\.');\r\n        const firebaseStoragePath = '(/([^?#]*).*)?$';\r\n        const firebaseStorageRegExp = new RegExp(`^https?://${firebaseStorageHost}/${version}/b/${bucketDomain}/o${firebaseStoragePath}`, 'i');\r\n        const firebaseStorageIndices = { bucket: 1, path: 3 };\r\n        const cloudStorageHost = host === DEFAULT_HOST\r\n            ? '(?:storage.googleapis.com|storage.cloud.google.com)'\r\n            : host;\r\n        const cloudStoragePath = '([^?#]*)';\r\n        const cloudStorageRegExp = new RegExp(`^https?://${cloudStorageHost}/${bucketDomain}/${cloudStoragePath}`, 'i');\r\n        const cloudStorageIndices = { bucket: 1, path: 2 };\r\n        const groups = [\r\n            { regex: gsRegex, indices: gsIndices, postModify: gsModify },\r\n            {\r\n                regex: firebaseStorageRegExp,\r\n                indices: firebaseStorageIndices,\r\n                postModify: httpModify\r\n            },\r\n            {\r\n                regex: cloudStorageRegExp,\r\n                indices: cloudStorageIndices,\r\n                postModify: httpModify\r\n            }\r\n        ];\r\n        for (let i = 0; i < groups.length; i++) {\r\n            const group = groups[i];\r\n            const captures = group.regex.exec(url);\r\n            if (captures) {\r\n                const bucketValue = captures[group.indices.bucket];\r\n                let pathValue = captures[group.indices.path];\r\n                if (!pathValue) {\r\n                    pathValue = '';\r\n                }\r\n                location = new Location(bucketValue, pathValue);\r\n                group.postModify(location);\r\n                break;\r\n            }\r\n        }\r\n        if (location == null) {\r\n            throw invalidUrl(url);\r\n        }\r\n        return location;\r\n    }\r\n}\n\n/**\r\n * A request whose promise always fails.\r\n */\r\nclass FailRequest {\r\n    constructor(error) {\r\n        this.promise_ = Promise.reject(error);\r\n    }\r\n    /** @inheritDoc */\r\n    getPromise() {\r\n        return this.promise_;\r\n    }\r\n    /** @inheritDoc */\r\n    cancel(_appDelete = false) { }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Accepts a callback for an action to perform (`doRequest`),\r\n * and then a callback for when the backoff has completed (`backoffCompleteCb`).\r\n * The callback sent to start requires an argument to call (`onRequestComplete`).\r\n * When `start` calls `doRequest`, it passes a callback for when the request has\r\n * completed, `onRequestComplete`. Based on this, the backoff continues, with\r\n * another call to `doRequest` and the above loop continues until the timeout\r\n * is hit, or a successful response occurs.\r\n * @description\r\n * @param doRequest Callback to perform request\r\n * @param backoffCompleteCb Callback to call when backoff has been completed\r\n */\r\nfunction start(doRequest, \r\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\r\nbackoffCompleteCb, timeout) {\r\n    // TODO(andysoto): make this code cleaner (probably refactor into an actual\r\n    // type instead of a bunch of functions with state shared in the closure)\r\n    let waitSeconds = 1;\r\n    // Would type this as \"number\" but that doesn't work for Node so ¯\\_(ツ)_/¯\r\n    // TODO: find a way to exclude Node type definition for storage because storage only works in browser\r\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n    let retryTimeoutId = null;\r\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n    let globalTimeoutId = null;\r\n    let hitTimeout = false;\r\n    let cancelState = 0;\r\n    function canceled() {\r\n        return cancelState === 2;\r\n    }\r\n    let triggeredCallback = false;\r\n    function triggerCallback(...args) {\r\n        if (!triggeredCallback) {\r\n            triggeredCallback = true;\r\n            backoffCompleteCb.apply(null, args);\r\n        }\r\n    }\r\n    function callWithDelay(millis) {\r\n        retryTimeoutId = setTimeout(() => {\r\n            retryTimeoutId = null;\r\n            doRequest(responseHandler, canceled());\r\n        }, millis);\r\n    }\r\n    function clearGlobalTimeout() {\r\n        if (globalTimeoutId) {\r\n            clearTimeout(globalTimeoutId);\r\n        }\r\n    }\r\n    function responseHandler(success, ...args) {\r\n        if (triggeredCallback) {\r\n            clearGlobalTimeout();\r\n            return;\r\n        }\r\n        if (success) {\r\n            clearGlobalTimeout();\r\n            triggerCallback.call(null, success, ...args);\r\n            return;\r\n        }\r\n        const mustStop = canceled() || hitTimeout;\r\n        if (mustStop) {\r\n            clearGlobalTimeout();\r\n            triggerCallback.call(null, success, ...args);\r\n            return;\r\n        }\r\n        if (waitSeconds < 64) {\r\n            /* TODO(andysoto): don't back off so quickly if we know we're offline. */\r\n            waitSeconds *= 2;\r\n        }\r\n        let waitMillis;\r\n        if (cancelState === 1) {\r\n            cancelState = 2;\r\n            waitMillis = 0;\r\n        }\r\n        else {\r\n            waitMillis = (waitSeconds + Math.random()) * 1000;\r\n        }\r\n        callWithDelay(waitMillis);\r\n    }\r\n    let stopped = false;\r\n    function stop(wasTimeout) {\r\n        if (stopped) {\r\n            return;\r\n        }\r\n        stopped = true;\r\n        clearGlobalTimeout();\r\n        if (triggeredCallback) {\r\n            return;\r\n        }\r\n        if (retryTimeoutId !== null) {\r\n            if (!wasTimeout) {\r\n                cancelState = 2;\r\n            }\r\n            clearTimeout(retryTimeoutId);\r\n            callWithDelay(0);\r\n        }\r\n        else {\r\n            if (!wasTimeout) {\r\n                cancelState = 1;\r\n            }\r\n        }\r\n    }\r\n    callWithDelay(0);\r\n    globalTimeoutId = setTimeout(() => {\r\n        hitTimeout = true;\r\n        stop(true);\r\n    }, timeout);\r\n    return stop;\r\n}\r\n/**\r\n * Stops the retry loop from repeating.\r\n * If the function is currently \"in between\" retries, it is invoked immediately\r\n * with the second parameter as \"true\". Otherwise, it will be invoked once more\r\n * after the current invocation finishes iff the current invocation would have\r\n * triggered another retry.\r\n */\r\nfunction stop(id) {\r\n    id(false);\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction isJustDef(p) {\r\n    return p !== void 0;\r\n}\r\n// eslint-disable-next-line @typescript-eslint/ban-types\r\nfunction isFunction(p) {\r\n    return typeof p === 'function';\r\n}\r\nfunction isNonArrayObject(p) {\r\n    return typeof p === 'object' && !Array.isArray(p);\r\n}\r\nfunction isString(p) {\r\n    return typeof p === 'string' || p instanceof String;\r\n}\r\nfunction isNativeBlob(p) {\r\n    return isNativeBlobDefined() && p instanceof Blob;\r\n}\r\nfunction isNativeBlobDefined() {\r\n    return typeof Blob !== 'undefined';\r\n}\r\nfunction validateNumber(argument, minValue, maxValue, value) {\r\n    if (value < minValue) {\r\n        throw invalidArgument(`Invalid value for '${argument}'. Expected ${minValue} or greater.`);\r\n    }\r\n    if (value > maxValue) {\r\n        throw invalidArgument(`Invalid value for '${argument}'. Expected ${maxValue} or less.`);\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction makeUrl(urlPart, host, protocol) {\r\n    let origin = host;\r\n    if (protocol == null) {\r\n        origin = `https://${host}`;\r\n    }\r\n    return `${protocol}://${origin}/v0${urlPart}`;\r\n}\r\nfunction makeQueryString(params) {\r\n    const encode = encodeURIComponent;\r\n    let queryPart = '?';\r\n    for (const key in params) {\r\n        if (params.hasOwnProperty(key)) {\r\n            const nextPart = encode(key) + '=' + encode(params[key]);\r\n            queryPart = queryPart + nextPart + '&';\r\n        }\r\n    }\r\n    // Chop off the extra '&' or '?' on the end\r\n    queryPart = queryPart.slice(0, -1);\r\n    return queryPart;\r\n}\n\n/**\r\n * Error codes for requests made by the XhrIo wrapper.\r\n */\r\nvar ErrorCode;\r\n(function (ErrorCode) {\r\n    ErrorCode[ErrorCode[\"NO_ERROR\"] = 0] = \"NO_ERROR\";\r\n    ErrorCode[ErrorCode[\"NETWORK_ERROR\"] = 1] = \"NETWORK_ERROR\";\r\n    ErrorCode[ErrorCode[\"ABORT\"] = 2] = \"ABORT\";\r\n})(ErrorCode || (ErrorCode = {}));\n\n/**\r\n * @license\r\n * Copyright 2022 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Checks the status code to see if the action should be retried.\r\n *\r\n * @param status Current HTTP status code returned by server.\r\n * @param additionalRetryCodes additional retry codes to check against\r\n */\r\nfunction isRetryStatusCode(status, additionalRetryCodes) {\r\n    // The codes for which to retry came from this page:\r\n    // https://cloud.google.com/storage/docs/exponential-backoff\r\n    const isFiveHundredCode = status >= 500 && status < 600;\r\n    const extraRetryCodes = [\r\n        // Request Timeout: web server didn't receive full request in time.\r\n        408,\r\n        // Too Many Requests: you're getting rate-limited, basically.\r\n        429\r\n    ];\r\n    const isExtraRetryCode = extraRetryCodes.indexOf(status) !== -1;\r\n    const isAdditionalRetryCode = additionalRetryCodes.indexOf(status) !== -1;\r\n    return isFiveHundredCode || isExtraRetryCode || isAdditionalRetryCode;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Handles network logic for all Storage Requests, including error reporting and\r\n * retries with backoff.\r\n *\r\n * @param I - the type of the backend's network response.\r\n * @param - O the output type used by the rest of the SDK. The conversion\r\n * happens in the specified `callback_`.\r\n */\r\nclass NetworkRequest {\r\n    constructor(url_, method_, headers_, body_, successCodes_, additionalRetryCodes_, callback_, errorCallback_, timeout_, progressCallback_, connectionFactory_, retry = true) {\r\n        this.url_ = url_;\r\n        this.method_ = method_;\r\n        this.headers_ = headers_;\r\n        this.body_ = body_;\r\n        this.successCodes_ = successCodes_;\r\n        this.additionalRetryCodes_ = additionalRetryCodes_;\r\n        this.callback_ = callback_;\r\n        this.errorCallback_ = errorCallback_;\r\n        this.timeout_ = timeout_;\r\n        this.progressCallback_ = progressCallback_;\r\n        this.connectionFactory_ = connectionFactory_;\r\n        this.retry = retry;\r\n        this.pendingConnection_ = null;\r\n        this.backoffId_ = null;\r\n        this.canceled_ = false;\r\n        this.appDelete_ = false;\r\n        this.promise_ = new Promise((resolve, reject) => {\r\n            this.resolve_ = resolve;\r\n            this.reject_ = reject;\r\n            this.start_();\r\n        });\r\n    }\r\n    /**\r\n     * Actually starts the retry loop.\r\n     */\r\n    start_() {\r\n        const doTheRequest = (backoffCallback, canceled) => {\r\n            if (canceled) {\r\n                backoffCallback(false, new RequestEndStatus(false, null, true));\r\n                return;\r\n            }\r\n            const connection = this.connectionFactory_();\r\n            this.pendingConnection_ = connection;\r\n            const progressListener = progressEvent => {\r\n                const loaded = progressEvent.loaded;\r\n                const total = progressEvent.lengthComputable ? progressEvent.total : -1;\r\n                if (this.progressCallback_ !== null) {\r\n                    this.progressCallback_(loaded, total);\r\n                }\r\n            };\r\n            if (this.progressCallback_ !== null) {\r\n                connection.addUploadProgressListener(progressListener);\r\n            }\r\n            // connection.send() never rejects, so we don't need to have a error handler or use catch on the returned promise.\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n            connection\r\n                .send(this.url_, this.method_, this.body_, this.headers_)\r\n                .then(() => {\r\n                if (this.progressCallback_ !== null) {\r\n                    connection.removeUploadProgressListener(progressListener);\r\n                }\r\n                this.pendingConnection_ = null;\r\n                const hitServer = connection.getErrorCode() === ErrorCode.NO_ERROR;\r\n                const status = connection.getStatus();\r\n                if (!hitServer ||\r\n                    (isRetryStatusCode(status, this.additionalRetryCodes_) &&\r\n                        this.retry)) {\r\n                    const wasCanceled = connection.getErrorCode() === ErrorCode.ABORT;\r\n                    backoffCallback(false, new RequestEndStatus(false, null, wasCanceled));\r\n                    return;\r\n                }\r\n                const successCode = this.successCodes_.indexOf(status) !== -1;\r\n                backoffCallback(true, new RequestEndStatus(successCode, connection));\r\n            });\r\n        };\r\n        /**\r\n         * @param requestWentThrough - True if the request eventually went\r\n         *     through, false if it hit the retry limit or was canceled.\r\n         */\r\n        const backoffDone = (requestWentThrough, status) => {\r\n            const resolve = this.resolve_;\r\n            const reject = this.reject_;\r\n            const connection = status.connection;\r\n            if (status.wasSuccessCode) {\r\n                try {\r\n                    const result = this.callback_(connection, connection.getResponse());\r\n                    if (isJustDef(result)) {\r\n                        resolve(result);\r\n                    }\r\n                    else {\r\n                        resolve();\r\n                    }\r\n                }\r\n                catch (e) {\r\n                    reject(e);\r\n                }\r\n            }\r\n            else {\r\n                if (connection !== null) {\r\n                    const err = unknown();\r\n                    err.serverResponse = connection.getErrorText();\r\n                    if (this.errorCallback_) {\r\n                        reject(this.errorCallback_(connection, err));\r\n                    }\r\n                    else {\r\n                        reject(err);\r\n                    }\r\n                }\r\n                else {\r\n                    if (status.canceled) {\r\n                        const err = this.appDelete_ ? appDeleted() : canceled();\r\n                        reject(err);\r\n                    }\r\n                    else {\r\n                        const err = retryLimitExceeded();\r\n                        reject(err);\r\n                    }\r\n                }\r\n            }\r\n        };\r\n        if (this.canceled_) {\r\n            backoffDone(false, new RequestEndStatus(false, null, true));\r\n        }\r\n        else {\r\n            this.backoffId_ = start(doTheRequest, backoffDone, this.timeout_);\r\n        }\r\n    }\r\n    /** @inheritDoc */\r\n    getPromise() {\r\n        return this.promise_;\r\n    }\r\n    /** @inheritDoc */\r\n    cancel(appDelete) {\r\n        this.canceled_ = true;\r\n        this.appDelete_ = appDelete || false;\r\n        if (this.backoffId_ !== null) {\r\n            stop(this.backoffId_);\r\n        }\r\n        if (this.pendingConnection_ !== null) {\r\n            this.pendingConnection_.abort();\r\n        }\r\n    }\r\n}\r\n/**\r\n * A collection of information about the result of a network request.\r\n * @param opt_canceled - Defaults to false.\r\n */\r\nclass RequestEndStatus {\r\n    constructor(wasSuccessCode, connection, canceled) {\r\n        this.wasSuccessCode = wasSuccessCode;\r\n        this.connection = connection;\r\n        this.canceled = !!canceled;\r\n    }\r\n}\r\nfunction addAuthHeader_(headers, authToken) {\r\n    if (authToken !== null && authToken.length > 0) {\r\n        headers['Authorization'] = 'Firebase ' + authToken;\r\n    }\r\n}\r\nfunction addVersionHeader_(headers, firebaseVersion) {\r\n    headers['X-Firebase-Storage-Version'] =\r\n        'webjs/' + (firebaseVersion !== null && firebaseVersion !== void 0 ? firebaseVersion : 'AppManager');\r\n}\r\nfunction addGmpidHeader_(headers, appId) {\r\n    if (appId) {\r\n        headers['X-Firebase-GMPID'] = appId;\r\n    }\r\n}\r\nfunction addAppCheckHeader_(headers, appCheckToken) {\r\n    if (appCheckToken !== null) {\r\n        headers['X-Firebase-AppCheck'] = appCheckToken;\r\n    }\r\n}\r\nfunction makeRequest(requestInfo, appId, authToken, appCheckToken, requestFactory, firebaseVersion, retry = true) {\r\n    const queryPart = makeQueryString(requestInfo.urlParams);\r\n    const url = requestInfo.url + queryPart;\r\n    const headers = Object.assign({}, requestInfo.headers);\r\n    addGmpidHeader_(headers, appId);\r\n    addAuthHeader_(headers, authToken);\r\n    addVersionHeader_(headers, firebaseVersion);\r\n    addAppCheckHeader_(headers, appCheckToken);\r\n    return new NetworkRequest(url, requestInfo.method, headers, requestInfo.body, requestInfo.successCodes, requestInfo.additionalRetryCodes, requestInfo.handler, requestInfo.errorHandler, requestInfo.timeout, requestInfo.progressCallback, requestFactory, retry);\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction getBlobBuilder() {\r\n    if (typeof BlobBuilder !== 'undefined') {\r\n        return BlobBuilder;\r\n    }\r\n    else if (typeof WebKitBlobBuilder !== 'undefined') {\r\n        return WebKitBlobBuilder;\r\n    }\r\n    else {\r\n        return undefined;\r\n    }\r\n}\r\n/**\r\n * Concatenates one or more values together and converts them to a Blob.\r\n *\r\n * @param args The values that will make up the resulting blob.\r\n * @return The blob.\r\n */\r\nfunction getBlob$1(...args) {\r\n    const BlobBuilder = getBlobBuilder();\r\n    if (BlobBuilder !== undefined) {\r\n        const bb = new BlobBuilder();\r\n        for (let i = 0; i < args.length; i++) {\r\n            bb.append(args[i]);\r\n        }\r\n        return bb.getBlob();\r\n    }\r\n    else {\r\n        if (isNativeBlobDefined()) {\r\n            return new Blob(args);\r\n        }\r\n        else {\r\n            throw new StorageError(StorageErrorCode.UNSUPPORTED_ENVIRONMENT, \"This browser doesn't seem to support creating Blobs\");\r\n        }\r\n    }\r\n}\r\n/**\r\n * Slices the blob. The returned blob contains data from the start byte\r\n * (inclusive) till the end byte (exclusive). Negative indices cannot be used.\r\n *\r\n * @param blob The blob to be sliced.\r\n * @param start Index of the starting byte.\r\n * @param end Index of the ending byte.\r\n * @return The blob slice or null if not supported.\r\n */\r\nfunction sliceBlob(blob, start, end) {\r\n    if (blob.webkitSlice) {\r\n        return blob.webkitSlice(start, end);\r\n    }\r\n    else if (blob.mozSlice) {\r\n        return blob.mozSlice(start, end);\r\n    }\r\n    else if (blob.slice) {\r\n        return blob.slice(start, end);\r\n    }\r\n    return null;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/** Converts a Base64 encoded string to a binary string. */\r\nfunction decodeBase64(encoded) {\r\n    // Node actually doesn't validate base64 strings.\r\n    // A quick sanity check that is not a fool-proof validation\r\n    if (/[^-A-Za-z0-9+/=]/.test(encoded)) {\r\n        throw invalidFormat('base64', 'Invalid character found');\r\n    }\r\n    return Buffer.from(encoded, 'base64').toString('binary');\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * An enumeration of the possible string formats for upload.\r\n * @public\r\n */\r\nconst StringFormat = {\r\n    /**\r\n     * Indicates the string should be interpreted \"raw\", that is, as normal text.\r\n     * The string will be interpreted as UTF-16, then uploaded as a UTF-8 byte\r\n     * sequence.\r\n     * Example: The string 'Hello! \\\\ud83d\\\\ude0a' becomes the byte sequence\r\n     * 48 65 6c 6c 6f 21 20 f0 9f 98 8a\r\n     */\r\n    RAW: 'raw',\r\n    /**\r\n     * Indicates the string should be interpreted as base64-encoded data.\r\n     * Padding characters (trailing '='s) are optional.\r\n     * Example: The string 'rWmO++E6t7/rlw==' becomes the byte sequence\r\n     * ad 69 8e fb e1 3a b7 bf eb 97\r\n     */\r\n    BASE64: 'base64',\r\n    /**\r\n     * Indicates the string should be interpreted as base64url-encoded data.\r\n     * Padding characters (trailing '='s) are optional.\r\n     * Example: The string 'rWmO--E6t7_rlw==' becomes the byte sequence\r\n     * ad 69 8e fb e1 3a b7 bf eb 97\r\n     */\r\n    BASE64URL: 'base64url',\r\n    /**\r\n     * Indicates the string is a data URL, such as one obtained from\r\n     * canvas.toDataURL().\r\n     * Example: the string 'data:application/octet-stream;base64,aaaa'\r\n     * becomes the byte sequence\r\n     * 69 a6 9a\r\n     * (the content-type \"application/octet-stream\" is also applied, but can\r\n     * be overridden in the metadata object).\r\n     */\r\n    DATA_URL: 'data_url'\r\n};\r\nclass StringData {\r\n    constructor(data, contentType) {\r\n        this.data = data;\r\n        this.contentType = contentType || null;\r\n    }\r\n}\r\n/**\r\n * @internal\r\n */\r\nfunction dataFromString(format, stringData) {\r\n    switch (format) {\r\n        case StringFormat.RAW:\r\n            return new StringData(utf8Bytes_(stringData));\r\n        case StringFormat.BASE64:\r\n        case StringFormat.BASE64URL:\r\n            return new StringData(base64Bytes_(format, stringData));\r\n        case StringFormat.DATA_URL:\r\n            return new StringData(dataURLBytes_(stringData), dataURLContentType_(stringData));\r\n        // do nothing\r\n    }\r\n    // assert(false);\r\n    throw unknown();\r\n}\r\nfunction utf8Bytes_(value) {\r\n    const b = [];\r\n    for (let i = 0; i < value.length; i++) {\r\n        let c = value.charCodeAt(i);\r\n        if (c <= 127) {\r\n            b.push(c);\r\n        }\r\n        else {\r\n            if (c <= 2047) {\r\n                b.push(192 | (c >> 6), 128 | (c & 63));\r\n            }\r\n            else {\r\n                if ((c & 64512) === 55296) {\r\n                    // The start of a surrogate pair.\r\n                    const valid = i < value.length - 1 && (value.charCodeAt(i + 1) & 64512) === 56320;\r\n                    if (!valid) {\r\n                        // The second surrogate wasn't there.\r\n                        b.push(239, 191, 189);\r\n                    }\r\n                    else {\r\n                        const hi = c;\r\n                        const lo = value.charCodeAt(++i);\r\n                        c = 65536 | ((hi & 1023) << 10) | (lo & 1023);\r\n                        b.push(240 | (c >> 18), 128 | ((c >> 12) & 63), 128 | ((c >> 6) & 63), 128 | (c & 63));\r\n                    }\r\n                }\r\n                else {\r\n                    if ((c & 64512) === 56320) {\r\n                        // Invalid low surrogate.\r\n                        b.push(239, 191, 189);\r\n                    }\r\n                    else {\r\n                        b.push(224 | (c >> 12), 128 | ((c >> 6) & 63), 128 | (c & 63));\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    return new Uint8Array(b);\r\n}\r\nfunction percentEncodedBytes_(value) {\r\n    let decoded;\r\n    try {\r\n        decoded = decodeURIComponent(value);\r\n    }\r\n    catch (e) {\r\n        throw invalidFormat(StringFormat.DATA_URL, 'Malformed data URL.');\r\n    }\r\n    return utf8Bytes_(decoded);\r\n}\r\nfunction base64Bytes_(format, value) {\r\n    switch (format) {\r\n        case StringFormat.BASE64: {\r\n            const hasMinus = value.indexOf('-') !== -1;\r\n            const hasUnder = value.indexOf('_') !== -1;\r\n            if (hasMinus || hasUnder) {\r\n                const invalidChar = hasMinus ? '-' : '_';\r\n                throw invalidFormat(format, \"Invalid character '\" +\r\n                    invalidChar +\r\n                    \"' found: is it base64url encoded?\");\r\n            }\r\n            break;\r\n        }\r\n        case StringFormat.BASE64URL: {\r\n            const hasPlus = value.indexOf('+') !== -1;\r\n            const hasSlash = value.indexOf('/') !== -1;\r\n            if (hasPlus || hasSlash) {\r\n                const invalidChar = hasPlus ? '+' : '/';\r\n                throw invalidFormat(format, \"Invalid character '\" + invalidChar + \"' found: is it base64 encoded?\");\r\n            }\r\n            value = value.replace(/-/g, '+').replace(/_/g, '/');\r\n            break;\r\n        }\r\n        // do nothing\r\n    }\r\n    let bytes;\r\n    try {\r\n        bytes = decodeBase64(value);\r\n    }\r\n    catch (e) {\r\n        if (e.message.includes('polyfill')) {\r\n            throw e;\r\n        }\r\n        throw invalidFormat(format, 'Invalid character found');\r\n    }\r\n    const array = new Uint8Array(bytes.length);\r\n    for (let i = 0; i < bytes.length; i++) {\r\n        array[i] = bytes.charCodeAt(i);\r\n    }\r\n    return array;\r\n}\r\nclass DataURLParts {\r\n    constructor(dataURL) {\r\n        this.base64 = false;\r\n        this.contentType = null;\r\n        const matches = dataURL.match(/^data:([^,]+)?,/);\r\n        if (matches === null) {\r\n            throw invalidFormat(StringFormat.DATA_URL, \"Must be formatted 'data:[<mediatype>][;base64],<data>\");\r\n        }\r\n        const middle = matches[1] || null;\r\n        if (middle != null) {\r\n            this.base64 = endsWith(middle, ';base64');\r\n            this.contentType = this.base64\r\n                ? middle.substring(0, middle.length - ';base64'.length)\r\n                : middle;\r\n        }\r\n        this.rest = dataURL.substring(dataURL.indexOf(',') + 1);\r\n    }\r\n}\r\nfunction dataURLBytes_(dataUrl) {\r\n    const parts = new DataURLParts(dataUrl);\r\n    if (parts.base64) {\r\n        return base64Bytes_(StringFormat.BASE64, parts.rest);\r\n    }\r\n    else {\r\n        return percentEncodedBytes_(parts.rest);\r\n    }\r\n}\r\nfunction dataURLContentType_(dataUrl) {\r\n    const parts = new DataURLParts(dataUrl);\r\n    return parts.contentType;\r\n}\r\nfunction endsWith(s, end) {\r\n    const longEnough = s.length >= end.length;\r\n    if (!longEnough) {\r\n        return false;\r\n    }\r\n    return s.substring(s.length - end.length) === end;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * @param opt_elideCopy - If true, doesn't copy mutable input data\r\n *     (e.g. Uint8Arrays). Pass true only if you know the objects will not be\r\n *     modified after this blob's construction.\r\n *\r\n * @internal\r\n */\r\nclass FbsBlob {\r\n    constructor(data, elideCopy) {\r\n        let size = 0;\r\n        let blobType = '';\r\n        if (isNativeBlob(data)) {\r\n            this.data_ = data;\r\n            size = data.size;\r\n            blobType = data.type;\r\n        }\r\n        else if (data instanceof ArrayBuffer) {\r\n            if (elideCopy) {\r\n                this.data_ = new Uint8Array(data);\r\n            }\r\n            else {\r\n                this.data_ = new Uint8Array(data.byteLength);\r\n                this.data_.set(new Uint8Array(data));\r\n            }\r\n            size = this.data_.length;\r\n        }\r\n        else if (data instanceof Uint8Array) {\r\n            if (elideCopy) {\r\n                this.data_ = data;\r\n            }\r\n            else {\r\n                this.data_ = new Uint8Array(data.length);\r\n                this.data_.set(data);\r\n            }\r\n            size = data.length;\r\n        }\r\n        this.size_ = size;\r\n        this.type_ = blobType;\r\n    }\r\n    size() {\r\n        return this.size_;\r\n    }\r\n    type() {\r\n        return this.type_;\r\n    }\r\n    slice(startByte, endByte) {\r\n        if (isNativeBlob(this.data_)) {\r\n            const realBlob = this.data_;\r\n            const sliced = sliceBlob(realBlob, startByte, endByte);\r\n            if (sliced === null) {\r\n                return null;\r\n            }\r\n            return new FbsBlob(sliced);\r\n        }\r\n        else {\r\n            const slice = new Uint8Array(this.data_.buffer, startByte, endByte - startByte);\r\n            return new FbsBlob(slice, true);\r\n        }\r\n    }\r\n    static getBlob(...args) {\r\n        if (isNativeBlobDefined()) {\r\n            const blobby = args.map((val) => {\r\n                if (val instanceof FbsBlob) {\r\n                    return val.data_;\r\n                }\r\n                else {\r\n                    return val;\r\n                }\r\n            });\r\n            return new FbsBlob(getBlob$1.apply(null, blobby));\r\n        }\r\n        else {\r\n            const uint8Arrays = args.map((val) => {\r\n                if (isString(val)) {\r\n                    return dataFromString(StringFormat.RAW, val).data;\r\n                }\r\n                else {\r\n                    // Blobs don't exist, so this has to be a Uint8Array.\r\n                    return val.data_;\r\n                }\r\n            });\r\n            let finalLength = 0;\r\n            uint8Arrays.forEach((array) => {\r\n                finalLength += array.byteLength;\r\n            });\r\n            const merged = new Uint8Array(finalLength);\r\n            let index = 0;\r\n            uint8Arrays.forEach((array) => {\r\n                for (let i = 0; i < array.length; i++) {\r\n                    merged[index++] = array[i];\r\n                }\r\n            });\r\n            return new FbsBlob(merged, true);\r\n        }\r\n    }\r\n    uploadData() {\r\n        return this.data_;\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Returns the Object resulting from parsing the given JSON, or null if the\r\n * given string does not represent a JSON object.\r\n */\r\nfunction jsonObjectOrNull(s) {\r\n    let obj;\r\n    try {\r\n        obj = JSON.parse(s);\r\n    }\r\n    catch (e) {\r\n        return null;\r\n    }\r\n    if (isNonArrayObject(obj)) {\r\n        return obj;\r\n    }\r\n    else {\r\n        return null;\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * @fileoverview Contains helper methods for manipulating paths.\r\n */\r\n/**\r\n * @return Null if the path is already at the root.\r\n */\r\nfunction parent(path) {\r\n    if (path.length === 0) {\r\n        return null;\r\n    }\r\n    const index = path.lastIndexOf('/');\r\n    if (index === -1) {\r\n        return '';\r\n    }\r\n    const newPath = path.slice(0, index);\r\n    return newPath;\r\n}\r\nfunction child(path, childPath) {\r\n    const canonicalChildPath = childPath\r\n        .split('/')\r\n        .filter(component => component.length > 0)\r\n        .join('/');\r\n    if (path.length === 0) {\r\n        return canonicalChildPath;\r\n    }\r\n    else {\r\n        return path + '/' + canonicalChildPath;\r\n    }\r\n}\r\n/**\r\n * Returns the last component of a path.\r\n * '/foo/bar' -> 'bar'\r\n * '/foo/bar/baz/' -> 'baz/'\r\n * '/a' -> 'a'\r\n */\r\nfunction lastComponent(path) {\r\n    const index = path.lastIndexOf('/', path.length - 2);\r\n    if (index === -1) {\r\n        return path;\r\n    }\r\n    else {\r\n        return path.slice(index + 1);\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction noXform_(metadata, value) {\r\n    return value;\r\n}\r\nclass Mapping {\r\n    constructor(server, local, writable, xform) {\r\n        this.server = server;\r\n        this.local = local || server;\r\n        this.writable = !!writable;\r\n        this.xform = xform || noXform_;\r\n    }\r\n}\r\nlet mappings_ = null;\r\nfunction xformPath(fullPath) {\r\n    if (!isString(fullPath) || fullPath.length < 2) {\r\n        return fullPath;\r\n    }\r\n    else {\r\n        return lastComponent(fullPath);\r\n    }\r\n}\r\nfunction getMappings() {\r\n    if (mappings_) {\r\n        return mappings_;\r\n    }\r\n    const mappings = [];\r\n    mappings.push(new Mapping('bucket'));\r\n    mappings.push(new Mapping('generation'));\r\n    mappings.push(new Mapping('metageneration'));\r\n    mappings.push(new Mapping('name', 'fullPath', true));\r\n    function mappingsXformPath(_metadata, fullPath) {\r\n        return xformPath(fullPath);\r\n    }\r\n    const nameMapping = new Mapping('name');\r\n    nameMapping.xform = mappingsXformPath;\r\n    mappings.push(nameMapping);\r\n    /**\r\n     * Coerces the second param to a number, if it is defined.\r\n     */\r\n    function xformSize(_metadata, size) {\r\n        if (size !== undefined) {\r\n            return Number(size);\r\n        }\r\n        else {\r\n            return size;\r\n        }\r\n    }\r\n    const sizeMapping = new Mapping('size');\r\n    sizeMapping.xform = xformSize;\r\n    mappings.push(sizeMapping);\r\n    mappings.push(new Mapping('timeCreated'));\r\n    mappings.push(new Mapping('updated'));\r\n    mappings.push(new Mapping('md5Hash', null, true));\r\n    mappings.push(new Mapping('cacheControl', null, true));\r\n    mappings.push(new Mapping('contentDisposition', null, true));\r\n    mappings.push(new Mapping('contentEncoding', null, true));\r\n    mappings.push(new Mapping('contentLanguage', null, true));\r\n    mappings.push(new Mapping('contentType', null, true));\r\n    mappings.push(new Mapping('metadata', 'customMetadata', true));\r\n    mappings_ = mappings;\r\n    return mappings_;\r\n}\r\nfunction addRef(metadata, service) {\r\n    function generateRef() {\r\n        const bucket = metadata['bucket'];\r\n        const path = metadata['fullPath'];\r\n        const loc = new Location(bucket, path);\r\n        return service._makeStorageReference(loc);\r\n    }\r\n    Object.defineProperty(metadata, 'ref', { get: generateRef });\r\n}\r\nfunction fromResource(service, resource, mappings) {\r\n    const metadata = {};\r\n    metadata['type'] = 'file';\r\n    const len = mappings.length;\r\n    for (let i = 0; i < len; i++) {\r\n        const mapping = mappings[i];\r\n        metadata[mapping.local] = mapping.xform(metadata, resource[mapping.server]);\r\n    }\r\n    addRef(metadata, service);\r\n    return metadata;\r\n}\r\nfunction fromResourceString(service, resourceString, mappings) {\r\n    const obj = jsonObjectOrNull(resourceString);\r\n    if (obj === null) {\r\n        return null;\r\n    }\r\n    const resource = obj;\r\n    return fromResource(service, resource, mappings);\r\n}\r\nfunction downloadUrlFromResourceString(metadata, resourceString, host, protocol) {\r\n    const obj = jsonObjectOrNull(resourceString);\r\n    if (obj === null) {\r\n        return null;\r\n    }\r\n    if (!isString(obj['downloadTokens'])) {\r\n        // This can happen if objects are uploaded through GCS and retrieved\r\n        // through list, so we don't want to throw an Error.\r\n        return null;\r\n    }\r\n    const tokens = obj['downloadTokens'];\r\n    if (tokens.length === 0) {\r\n        return null;\r\n    }\r\n    const encode = encodeURIComponent;\r\n    const tokensList = tokens.split(',');\r\n    const urls = tokensList.map((token) => {\r\n        const bucket = metadata['bucket'];\r\n        const path = metadata['fullPath'];\r\n        const urlPart = '/b/' + encode(bucket) + '/o/' + encode(path);\r\n        const base = makeUrl(urlPart, host, protocol);\r\n        const queryString = makeQueryString({\r\n            alt: 'media',\r\n            token\r\n        });\r\n        return base + queryString;\r\n    });\r\n    return urls[0];\r\n}\r\nfunction toResourceString(metadata, mappings) {\r\n    const resource = {};\r\n    const len = mappings.length;\r\n    for (let i = 0; i < len; i++) {\r\n        const mapping = mappings[i];\r\n        if (mapping.writable) {\r\n            resource[mapping.server] = metadata[mapping.local];\r\n        }\r\n    }\r\n    return JSON.stringify(resource);\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst PREFIXES_KEY = 'prefixes';\r\nconst ITEMS_KEY = 'items';\r\nfunction fromBackendResponse(service, bucket, resource) {\r\n    const listResult = {\r\n        prefixes: [],\r\n        items: [],\r\n        nextPageToken: resource['nextPageToken']\r\n    };\r\n    if (resource[PREFIXES_KEY]) {\r\n        for (const path of resource[PREFIXES_KEY]) {\r\n            const pathWithoutTrailingSlash = path.replace(/\\/$/, '');\r\n            const reference = service._makeStorageReference(new Location(bucket, pathWithoutTrailingSlash));\r\n            listResult.prefixes.push(reference);\r\n        }\r\n    }\r\n    if (resource[ITEMS_KEY]) {\r\n        for (const item of resource[ITEMS_KEY]) {\r\n            const reference = service._makeStorageReference(new Location(bucket, item['name']));\r\n            listResult.items.push(reference);\r\n        }\r\n    }\r\n    return listResult;\r\n}\r\nfunction fromResponseString(service, bucket, resourceString) {\r\n    const obj = jsonObjectOrNull(resourceString);\r\n    if (obj === null) {\r\n        return null;\r\n    }\r\n    const resource = obj;\r\n    return fromBackendResponse(service, bucket, resource);\r\n}\n\n/**\r\n * Contains a fully specified request.\r\n *\r\n * @param I - the type of the backend's network response.\r\n * @param O - the output response type used by the rest of the SDK.\r\n */\r\nclass RequestInfo {\r\n    constructor(url, method, \r\n    /**\r\n     * Returns the value with which to resolve the request's promise. Only called\r\n     * if the request is successful. Throw from this function to reject the\r\n     * returned Request's promise with the thrown error.\r\n     * Note: The XhrIo passed to this function may be reused after this callback\r\n     * returns. Do not keep a reference to it in any way.\r\n     */\r\n    handler, timeout) {\r\n        this.url = url;\r\n        this.method = method;\r\n        this.handler = handler;\r\n        this.timeout = timeout;\r\n        this.urlParams = {};\r\n        this.headers = {};\r\n        this.body = null;\r\n        this.errorHandler = null;\r\n        /**\r\n         * Called with the current number of bytes uploaded and total size (-1 if not\r\n         * computable) of the request body (i.e. used to report upload progress).\r\n         */\r\n        this.progressCallback = null;\r\n        this.successCodes = [200];\r\n        this.additionalRetryCodes = [];\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Throws the UNKNOWN StorageError if cndn is false.\r\n */\r\nfunction handlerCheck(cndn) {\r\n    if (!cndn) {\r\n        throw unknown();\r\n    }\r\n}\r\nfunction metadataHandler(service, mappings) {\r\n    function handler(xhr, text) {\r\n        const metadata = fromResourceString(service, text, mappings);\r\n        handlerCheck(metadata !== null);\r\n        return metadata;\r\n    }\r\n    return handler;\r\n}\r\nfunction listHandler(service, bucket) {\r\n    function handler(xhr, text) {\r\n        const listResult = fromResponseString(service, bucket, text);\r\n        handlerCheck(listResult !== null);\r\n        return listResult;\r\n    }\r\n    return handler;\r\n}\r\nfunction downloadUrlHandler(service, mappings) {\r\n    function handler(xhr, text) {\r\n        const metadata = fromResourceString(service, text, mappings);\r\n        handlerCheck(metadata !== null);\r\n        return downloadUrlFromResourceString(metadata, text, service.host, service._protocol);\r\n    }\r\n    return handler;\r\n}\r\nfunction sharedErrorHandler(location) {\r\n    function errorHandler(xhr, err) {\r\n        let newErr;\r\n        if (xhr.getStatus() === 401) {\r\n            if (\r\n            // This exact message string is the only consistent part of the\r\n            // server's error response that identifies it as an App Check error.\r\n            xhr.getErrorText().includes('Firebase App Check token is invalid')) {\r\n                newErr = unauthorizedApp();\r\n            }\r\n            else {\r\n                newErr = unauthenticated();\r\n            }\r\n        }\r\n        else {\r\n            if (xhr.getStatus() === 402) {\r\n                newErr = quotaExceeded(location.bucket);\r\n            }\r\n            else {\r\n                if (xhr.getStatus() === 403) {\r\n                    newErr = unauthorized(location.path);\r\n                }\r\n                else {\r\n                    newErr = err;\r\n                }\r\n            }\r\n        }\r\n        newErr.status = xhr.getStatus();\r\n        newErr.serverResponse = err.serverResponse;\r\n        return newErr;\r\n    }\r\n    return errorHandler;\r\n}\r\nfunction objectErrorHandler(location) {\r\n    const shared = sharedErrorHandler(location);\r\n    function errorHandler(xhr, err) {\r\n        let newErr = shared(xhr, err);\r\n        if (xhr.getStatus() === 404) {\r\n            newErr = objectNotFound(location.path);\r\n        }\r\n        newErr.serverResponse = err.serverResponse;\r\n        return newErr;\r\n    }\r\n    return errorHandler;\r\n}\r\nfunction getMetadata$2(service, location, mappings) {\r\n    const urlPart = location.fullServerUrl();\r\n    const url = makeUrl(urlPart, service.host, service._protocol);\r\n    const method = 'GET';\r\n    const timeout = service.maxOperationRetryTime;\r\n    const requestInfo = new RequestInfo(url, method, metadataHandler(service, mappings), timeout);\r\n    requestInfo.errorHandler = objectErrorHandler(location);\r\n    return requestInfo;\r\n}\r\nfunction list$2(service, location, delimiter, pageToken, maxResults) {\r\n    const urlParams = {};\r\n    if (location.isRoot) {\r\n        urlParams['prefix'] = '';\r\n    }\r\n    else {\r\n        urlParams['prefix'] = location.path + '/';\r\n    }\r\n    if (delimiter && delimiter.length > 0) {\r\n        urlParams['delimiter'] = delimiter;\r\n    }\r\n    if (pageToken) {\r\n        urlParams['pageToken'] = pageToken;\r\n    }\r\n    if (maxResults) {\r\n        urlParams['maxResults'] = maxResults;\r\n    }\r\n    const urlPart = location.bucketOnlyServerUrl();\r\n    const url = makeUrl(urlPart, service.host, service._protocol);\r\n    const method = 'GET';\r\n    const timeout = service.maxOperationRetryTime;\r\n    const requestInfo = new RequestInfo(url, method, listHandler(service, location.bucket), timeout);\r\n    requestInfo.urlParams = urlParams;\r\n    requestInfo.errorHandler = sharedErrorHandler(location);\r\n    return requestInfo;\r\n}\r\nfunction getBytes$1(service, location, maxDownloadSizeBytes) {\r\n    const urlPart = location.fullServerUrl();\r\n    const url = makeUrl(urlPart, service.host, service._protocol) + '?alt=media';\r\n    const method = 'GET';\r\n    const timeout = service.maxOperationRetryTime;\r\n    const requestInfo = new RequestInfo(url, method, (_, data) => data, timeout);\r\n    requestInfo.errorHandler = objectErrorHandler(location);\r\n    if (maxDownloadSizeBytes !== undefined) {\r\n        requestInfo.headers['Range'] = `bytes=0-${maxDownloadSizeBytes}`;\r\n        requestInfo.successCodes = [200 /* OK */, 206 /* Partial Content */];\r\n    }\r\n    return requestInfo;\r\n}\r\nfunction getDownloadUrl(service, location, mappings) {\r\n    const urlPart = location.fullServerUrl();\r\n    const url = makeUrl(urlPart, service.host, service._protocol);\r\n    const method = 'GET';\r\n    const timeout = service.maxOperationRetryTime;\r\n    const requestInfo = new RequestInfo(url, method, downloadUrlHandler(service, mappings), timeout);\r\n    requestInfo.errorHandler = objectErrorHandler(location);\r\n    return requestInfo;\r\n}\r\nfunction updateMetadata$2(service, location, metadata, mappings) {\r\n    const urlPart = location.fullServerUrl();\r\n    const url = makeUrl(urlPart, service.host, service._protocol);\r\n    const method = 'PATCH';\r\n    const body = toResourceString(metadata, mappings);\r\n    const headers = { 'Content-Type': 'application/json; charset=utf-8' };\r\n    const timeout = service.maxOperationRetryTime;\r\n    const requestInfo = new RequestInfo(url, method, metadataHandler(service, mappings), timeout);\r\n    requestInfo.headers = headers;\r\n    requestInfo.body = body;\r\n    requestInfo.errorHandler = objectErrorHandler(location);\r\n    return requestInfo;\r\n}\r\nfunction deleteObject$2(service, location) {\r\n    const urlPart = location.fullServerUrl();\r\n    const url = makeUrl(urlPart, service.host, service._protocol);\r\n    const method = 'DELETE';\r\n    const timeout = service.maxOperationRetryTime;\r\n    function handler(_xhr, _text) { }\r\n    const requestInfo = new RequestInfo(url, method, handler, timeout);\r\n    requestInfo.successCodes = [200, 204];\r\n    requestInfo.errorHandler = objectErrorHandler(location);\r\n    return requestInfo;\r\n}\r\nfunction determineContentType_(metadata, blob) {\r\n    return ((metadata && metadata['contentType']) ||\r\n        (blob && blob.type()) ||\r\n        'application/octet-stream');\r\n}\r\nfunction metadataForUpload_(location, blob, metadata) {\r\n    const metadataClone = Object.assign({}, metadata);\r\n    metadataClone['fullPath'] = location.path;\r\n    metadataClone['size'] = blob.size();\r\n    if (!metadataClone['contentType']) {\r\n        metadataClone['contentType'] = determineContentType_(null, blob);\r\n    }\r\n    return metadataClone;\r\n}\r\n/**\r\n * Prepare RequestInfo for uploads as Content-Type: multipart.\r\n */\r\nfunction multipartUpload(service, location, mappings, blob, metadata) {\r\n    const urlPart = location.bucketOnlyServerUrl();\r\n    const headers = {\r\n        'X-Goog-Upload-Protocol': 'multipart'\r\n    };\r\n    function genBoundary() {\r\n        let str = '';\r\n        for (let i = 0; i < 2; i++) {\r\n            str = str + Math.random().toString().slice(2);\r\n        }\r\n        return str;\r\n    }\r\n    const boundary = genBoundary();\r\n    headers['Content-Type'] = 'multipart/related; boundary=' + boundary;\r\n    const metadata_ = metadataForUpload_(location, blob, metadata);\r\n    const metadataString = toResourceString(metadata_, mappings);\r\n    const preBlobPart = '--' +\r\n        boundary +\r\n        '\\r\\n' +\r\n        'Content-Type: application/json; charset=utf-8\\r\\n\\r\\n' +\r\n        metadataString +\r\n        '\\r\\n--' +\r\n        boundary +\r\n        '\\r\\n' +\r\n        'Content-Type: ' +\r\n        metadata_['contentType'] +\r\n        '\\r\\n\\r\\n';\r\n    const postBlobPart = '\\r\\n--' + boundary + '--';\r\n    const body = FbsBlob.getBlob(preBlobPart, blob, postBlobPart);\r\n    if (body === null) {\r\n        throw cannotSliceBlob();\r\n    }\r\n    const urlParams = { name: metadata_['fullPath'] };\r\n    const url = makeUrl(urlPart, service.host, service._protocol);\r\n    const method = 'POST';\r\n    const timeout = service.maxUploadRetryTime;\r\n    const requestInfo = new RequestInfo(url, method, metadataHandler(service, mappings), timeout);\r\n    requestInfo.urlParams = urlParams;\r\n    requestInfo.headers = headers;\r\n    requestInfo.body = body.uploadData();\r\n    requestInfo.errorHandler = sharedErrorHandler(location);\r\n    return requestInfo;\r\n}\r\n/**\r\n * @param current The number of bytes that have been uploaded so far.\r\n * @param total The total number of bytes in the upload.\r\n * @param opt_finalized True if the server has finished the upload.\r\n * @param opt_metadata The upload metadata, should\r\n *     only be passed if opt_finalized is true.\r\n */\r\nclass ResumableUploadStatus {\r\n    constructor(current, total, finalized, metadata) {\r\n        this.current = current;\r\n        this.total = total;\r\n        this.finalized = !!finalized;\r\n        this.metadata = metadata || null;\r\n    }\r\n}\r\nfunction checkResumeHeader_(xhr, allowed) {\r\n    let status = null;\r\n    try {\r\n        status = xhr.getResponseHeader('X-Goog-Upload-Status');\r\n    }\r\n    catch (e) {\r\n        handlerCheck(false);\r\n    }\r\n    const allowedStatus = allowed || ['active'];\r\n    handlerCheck(!!status && allowedStatus.indexOf(status) !== -1);\r\n    return status;\r\n}\r\nfunction createResumableUpload(service, location, mappings, blob, metadata) {\r\n    const urlPart = location.bucketOnlyServerUrl();\r\n    const metadataForUpload = metadataForUpload_(location, blob, metadata);\r\n    const urlParams = { name: metadataForUpload['fullPath'] };\r\n    const url = makeUrl(urlPart, service.host, service._protocol);\r\n    const method = 'POST';\r\n    const headers = {\r\n        'X-Goog-Upload-Protocol': 'resumable',\r\n        'X-Goog-Upload-Command': 'start',\r\n        'X-Goog-Upload-Header-Content-Length': `${blob.size()}`,\r\n        'X-Goog-Upload-Header-Content-Type': metadataForUpload['contentType'],\r\n        'Content-Type': 'application/json; charset=utf-8'\r\n    };\r\n    const body = toResourceString(metadataForUpload, mappings);\r\n    const timeout = service.maxUploadRetryTime;\r\n    function handler(xhr) {\r\n        checkResumeHeader_(xhr);\r\n        let url;\r\n        try {\r\n            url = xhr.getResponseHeader('X-Goog-Upload-URL');\r\n        }\r\n        catch (e) {\r\n            handlerCheck(false);\r\n        }\r\n        handlerCheck(isString(url));\r\n        return url;\r\n    }\r\n    const requestInfo = new RequestInfo(url, method, handler, timeout);\r\n    requestInfo.urlParams = urlParams;\r\n    requestInfo.headers = headers;\r\n    requestInfo.body = body;\r\n    requestInfo.errorHandler = sharedErrorHandler(location);\r\n    return requestInfo;\r\n}\r\n/**\r\n * @param url From a call to fbs.requests.createResumableUpload.\r\n */\r\nfunction getResumableUploadStatus(service, location, url, blob) {\r\n    const headers = { 'X-Goog-Upload-Command': 'query' };\r\n    function handler(xhr) {\r\n        const status = checkResumeHeader_(xhr, ['active', 'final']);\r\n        let sizeString = null;\r\n        try {\r\n            sizeString = xhr.getResponseHeader('X-Goog-Upload-Size-Received');\r\n        }\r\n        catch (e) {\r\n            handlerCheck(false);\r\n        }\r\n        if (!sizeString) {\r\n            // null or empty string\r\n            handlerCheck(false);\r\n        }\r\n        const size = Number(sizeString);\r\n        handlerCheck(!isNaN(size));\r\n        return new ResumableUploadStatus(size, blob.size(), status === 'final');\r\n    }\r\n    const method = 'POST';\r\n    const timeout = service.maxUploadRetryTime;\r\n    const requestInfo = new RequestInfo(url, method, handler, timeout);\r\n    requestInfo.headers = headers;\r\n    requestInfo.errorHandler = sharedErrorHandler(location);\r\n    return requestInfo;\r\n}\r\n/**\r\n * Any uploads via the resumable upload API must transfer a number of bytes\r\n * that is a multiple of this number.\r\n */\r\nconst RESUMABLE_UPLOAD_CHUNK_SIZE = 256 * 1024;\r\n/**\r\n * @param url From a call to fbs.requests.createResumableUpload.\r\n * @param chunkSize Number of bytes to upload.\r\n * @param status The previous status.\r\n *     If not passed or null, we start from the beginning.\r\n * @throws fbs.Error If the upload is already complete, the passed in status\r\n *     has a final size inconsistent with the blob, or the blob cannot be sliced\r\n *     for upload.\r\n */\r\nfunction continueResumableUpload(location, service, url, blob, chunkSize, mappings, status, progressCallback) {\r\n    // TODO(andysoto): standardize on internal asserts\r\n    // assert(!(opt_status && opt_status.finalized));\r\n    const status_ = new ResumableUploadStatus(0, 0);\r\n    if (status) {\r\n        status_.current = status.current;\r\n        status_.total = status.total;\r\n    }\r\n    else {\r\n        status_.current = 0;\r\n        status_.total = blob.size();\r\n    }\r\n    if (blob.size() !== status_.total) {\r\n        throw serverFileWrongSize();\r\n    }\r\n    const bytesLeft = status_.total - status_.current;\r\n    let bytesToUpload = bytesLeft;\r\n    if (chunkSize > 0) {\r\n        bytesToUpload = Math.min(bytesToUpload, chunkSize);\r\n    }\r\n    const startByte = status_.current;\r\n    const endByte = startByte + bytesToUpload;\r\n    let uploadCommand = '';\r\n    if (bytesToUpload === 0) {\r\n        uploadCommand = 'finalize';\r\n    }\r\n    else if (bytesLeft === bytesToUpload) {\r\n        uploadCommand = 'upload, finalize';\r\n    }\r\n    else {\r\n        uploadCommand = 'upload';\r\n    }\r\n    const headers = {\r\n        'X-Goog-Upload-Command': uploadCommand,\r\n        'X-Goog-Upload-Offset': `${status_.current}`\r\n    };\r\n    const body = blob.slice(startByte, endByte);\r\n    if (body === null) {\r\n        throw cannotSliceBlob();\r\n    }\r\n    function handler(xhr, text) {\r\n        // TODO(andysoto): Verify the MD5 of each uploaded range:\r\n        // the 'x-range-md5' header comes back with status code 308 responses.\r\n        // We'll only be able to bail out though, because you can't re-upload a\r\n        // range that you previously uploaded.\r\n        const uploadStatus = checkResumeHeader_(xhr, ['active', 'final']);\r\n        const newCurrent = status_.current + bytesToUpload;\r\n        const size = blob.size();\r\n        let metadata;\r\n        if (uploadStatus === 'final') {\r\n            metadata = metadataHandler(service, mappings)(xhr, text);\r\n        }\r\n        else {\r\n            metadata = null;\r\n        }\r\n        return new ResumableUploadStatus(newCurrent, size, uploadStatus === 'final', metadata);\r\n    }\r\n    const method = 'POST';\r\n    const timeout = service.maxUploadRetryTime;\r\n    const requestInfo = new RequestInfo(url, method, handler, timeout);\r\n    requestInfo.headers = headers;\r\n    requestInfo.body = body.uploadData();\r\n    requestInfo.progressCallback = progressCallback || null;\r\n    requestInfo.errorHandler = sharedErrorHandler(location);\r\n    return requestInfo;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * An event that is triggered on a task.\r\n * @internal\r\n */\r\nconst TaskEvent = {\r\n    /**\r\n     * For this event,\r\n     * <ul>\r\n     *   <li>The `next` function is triggered on progress updates and when the\r\n     *       task is paused/resumed with an `UploadTaskSnapshot` as the first\r\n     *       argument.</li>\r\n     *   <li>The `error` function is triggered if the upload is canceled or fails\r\n     *       for another reason.</li>\r\n     *   <li>The `complete` function is triggered if the upload completes\r\n     *       successfully.</li>\r\n     * </ul>\r\n     */\r\n    STATE_CHANGED: 'state_changed'\r\n};\r\n// type keys = keyof TaskState\r\n/**\r\n * Represents the current state of a running upload.\r\n * @internal\r\n */\r\nconst TaskState = {\r\n    /** The task is currently transferring data. */\r\n    RUNNING: 'running',\r\n    /** The task was paused by the user. */\r\n    PAUSED: 'paused',\r\n    /** The task completed successfully. */\r\n    SUCCESS: 'success',\r\n    /** The task was canceled. */\r\n    CANCELED: 'canceled',\r\n    /** The task failed with an error. */\r\n    ERROR: 'error'\r\n};\r\nfunction taskStateFromInternalTaskState(state) {\r\n    switch (state) {\r\n        case \"running\" /* InternalTaskState.RUNNING */:\r\n        case \"pausing\" /* InternalTaskState.PAUSING */:\r\n        case \"canceling\" /* InternalTaskState.CANCELING */:\r\n            return TaskState.RUNNING;\r\n        case \"paused\" /* InternalTaskState.PAUSED */:\r\n            return TaskState.PAUSED;\r\n        case \"success\" /* InternalTaskState.SUCCESS */:\r\n            return TaskState.SUCCESS;\r\n        case \"canceled\" /* InternalTaskState.CANCELED */:\r\n            return TaskState.CANCELED;\r\n        case \"error\" /* InternalTaskState.ERROR */:\r\n            return TaskState.ERROR;\r\n        default:\r\n            // TODO(andysoto): assert(false);\r\n            return TaskState.ERROR;\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nclass Observer {\r\n    constructor(nextOrObserver, error, complete) {\r\n        const asFunctions = isFunction(nextOrObserver) || error != null || complete != null;\r\n        if (asFunctions) {\r\n            this.next = nextOrObserver;\r\n            this.error = error !== null && error !== void 0 ? error : undefined;\r\n            this.complete = complete !== null && complete !== void 0 ? complete : undefined;\r\n        }\r\n        else {\r\n            const observer = nextOrObserver;\r\n            this.next = observer.next;\r\n            this.error = observer.error;\r\n            this.complete = observer.complete;\r\n        }\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Returns a function that invokes f with its arguments asynchronously as a\r\n * microtask, i.e. as soon as possible after the current script returns back\r\n * into browser code.\r\n */\r\n// eslint-disable-next-line @typescript-eslint/ban-types\r\nfunction async(f) {\r\n    return (...argsToForward) => {\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        Promise.resolve().then(() => f(...argsToForward));\r\n    };\r\n}\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/** An override for the text-based Connection. Used in tests. */\r\nlet textFactoryOverride = null;\r\n/**\r\n * Network layer that works in Node.\r\n *\r\n * This network implementation should not be used in browsers as it does not\r\n * support progress updates.\r\n */\r\nclass FetchConnection {\r\n    constructor() {\r\n        this.errorText_ = '';\r\n        this.sent_ = false;\r\n        this.fetch_ = undici__WEBPACK_IMPORTED_MODULE_2__.fetch;\r\n        this.errorCode_ = ErrorCode.NO_ERROR;\r\n    }\r\n    async send(url, method, body, headers) {\r\n        if (this.sent_) {\r\n            throw internalError('cannot .send() more than once');\r\n        }\r\n        this.sent_ = true;\r\n        try {\r\n            const response = await this.fetch_(url, {\r\n                method,\r\n                headers: headers || {},\r\n                body: body\r\n            });\r\n            this.headers_ = response.headers;\r\n            this.statusCode_ = response.status;\r\n            this.errorCode_ = ErrorCode.NO_ERROR;\r\n            this.body_ = await response.arrayBuffer();\r\n        }\r\n        catch (e) {\r\n            this.errorText_ = e === null || e === void 0 ? void 0 : e.message;\r\n            // emulate XHR which sets status to 0 when encountering a network error\r\n            this.statusCode_ = 0;\r\n            this.errorCode_ = ErrorCode.NETWORK_ERROR;\r\n        }\r\n    }\r\n    getErrorCode() {\r\n        if (this.errorCode_ === undefined) {\r\n            throw internalError('cannot .getErrorCode() before receiving response');\r\n        }\r\n        return this.errorCode_;\r\n    }\r\n    getStatus() {\r\n        if (this.statusCode_ === undefined) {\r\n            throw internalError('cannot .getStatus() before receiving response');\r\n        }\r\n        return this.statusCode_;\r\n    }\r\n    getErrorText() {\r\n        return this.errorText_;\r\n    }\r\n    abort() {\r\n        // Not supported\r\n    }\r\n    getResponseHeader(header) {\r\n        if (!this.headers_) {\r\n            throw internalError('cannot .getResponseHeader() before receiving response');\r\n        }\r\n        return this.headers_.get(header);\r\n    }\r\n    addUploadProgressListener(listener) {\r\n        // Not supported\r\n    }\r\n    removeUploadProgressListener(listener) {\r\n        // Not supported\r\n    }\r\n}\r\nclass FetchTextConnection extends FetchConnection {\r\n    getResponse() {\r\n        if (!this.body_) {\r\n            throw internalError('cannot .getResponse() before receiving response');\r\n        }\r\n        return Buffer.from(this.body_).toString('utf-8');\r\n    }\r\n}\r\nfunction newTextConnection() {\r\n    return textFactoryOverride\r\n        ? textFactoryOverride()\r\n        : new FetchTextConnection();\r\n}\r\nclass FetchBytesConnection extends FetchConnection {\r\n    getResponse() {\r\n        if (!this.body_) {\r\n            throw internalError('cannot .getResponse() before sending');\r\n        }\r\n        return this.body_;\r\n    }\r\n}\r\nfunction newBytesConnection() {\r\n    return new FetchBytesConnection();\r\n}\r\nclass FetchStreamConnection extends FetchConnection {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.stream_ = null;\r\n    }\r\n    async send(url, method, body, headers) {\r\n        if (this.sent_) {\r\n            throw internalError('cannot .send() more than once');\r\n        }\r\n        this.sent_ = true;\r\n        try {\r\n            const response = await this.fetch_(url, {\r\n                method,\r\n                headers: headers || {},\r\n                body: body\r\n            });\r\n            this.headers_ = response.headers;\r\n            this.statusCode_ = response.status;\r\n            this.errorCode_ = ErrorCode.NO_ERROR;\r\n            this.stream_ = response.body;\r\n        }\r\n        catch (e) {\r\n            this.errorText_ = e === null || e === void 0 ? void 0 : e.message;\r\n            // emulate XHR which sets status to 0 when encountering a network error\r\n            this.statusCode_ = 0;\r\n            this.errorCode_ = ErrorCode.NETWORK_ERROR;\r\n        }\r\n    }\r\n    getResponse() {\r\n        if (!this.stream_) {\r\n            throw internalError('cannot .getResponse() before sending');\r\n        }\r\n        return this.stream_;\r\n    }\r\n}\r\nfunction newStreamConnection() {\r\n    return new FetchStreamConnection();\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Represents a blob being uploaded. Can be used to pause/resume/cancel the\r\n * upload and manage callbacks for various events.\r\n * @internal\r\n */\r\nclass UploadTask {\r\n    /**\r\n     * @param ref - The firebaseStorage.Reference object this task came\r\n     *     from, untyped to avoid cyclic dependencies.\r\n     * @param blob - The blob to upload.\r\n     */\r\n    constructor(ref, blob, metadata = null) {\r\n        /**\r\n         * Number of bytes transferred so far.\r\n         */\r\n        this._transferred = 0;\r\n        this._needToFetchStatus = false;\r\n        this._needToFetchMetadata = false;\r\n        this._observers = [];\r\n        this._error = undefined;\r\n        this._uploadUrl = undefined;\r\n        this._request = undefined;\r\n        this._chunkMultiplier = 1;\r\n        this._resolve = undefined;\r\n        this._reject = undefined;\r\n        this._ref = ref;\r\n        this._blob = blob;\r\n        this._metadata = metadata;\r\n        this._mappings = getMappings();\r\n        this._resumable = this._shouldDoResumable(this._blob);\r\n        this._state = \"running\" /* InternalTaskState.RUNNING */;\r\n        this._errorHandler = error => {\r\n            this._request = undefined;\r\n            this._chunkMultiplier = 1;\r\n            if (error._codeEquals(StorageErrorCode.CANCELED)) {\r\n                this._needToFetchStatus = true;\r\n                this.completeTransitions_();\r\n            }\r\n            else {\r\n                const backoffExpired = this.isExponentialBackoffExpired();\r\n                if (isRetryStatusCode(error.status, [])) {\r\n                    if (backoffExpired) {\r\n                        error = retryLimitExceeded();\r\n                    }\r\n                    else {\r\n                        this.sleepTime = Math.max(this.sleepTime * 2, DEFAULT_MIN_SLEEP_TIME_MILLIS);\r\n                        this._needToFetchStatus = true;\r\n                        this.completeTransitions_();\r\n                        return;\r\n                    }\r\n                }\r\n                this._error = error;\r\n                this._transition(\"error\" /* InternalTaskState.ERROR */);\r\n            }\r\n        };\r\n        this._metadataErrorHandler = error => {\r\n            this._request = undefined;\r\n            if (error._codeEquals(StorageErrorCode.CANCELED)) {\r\n                this.completeTransitions_();\r\n            }\r\n            else {\r\n                this._error = error;\r\n                this._transition(\"error\" /* InternalTaskState.ERROR */);\r\n            }\r\n        };\r\n        this.sleepTime = 0;\r\n        this.maxSleepTime = this._ref.storage.maxUploadRetryTime;\r\n        this._promise = new Promise((resolve, reject) => {\r\n            this._resolve = resolve;\r\n            this._reject = reject;\r\n            this._start();\r\n        });\r\n        // Prevent uncaught rejections on the internal promise from bubbling out\r\n        // to the top level with a dummy handler.\r\n        this._promise.then(null, () => { });\r\n    }\r\n    isExponentialBackoffExpired() {\r\n        return this.sleepTime > this.maxSleepTime;\r\n    }\r\n    _makeProgressCallback() {\r\n        const sizeBefore = this._transferred;\r\n        return loaded => this._updateProgress(sizeBefore + loaded);\r\n    }\r\n    _shouldDoResumable(blob) {\r\n        return blob.size() > 256 * 1024;\r\n    }\r\n    _start() {\r\n        if (this._state !== \"running\" /* InternalTaskState.RUNNING */) {\r\n            // This can happen if someone pauses us in a resume callback, for example.\r\n            return;\r\n        }\r\n        if (this._request !== undefined) {\r\n            return;\r\n        }\r\n        if (this._resumable) {\r\n            if (this._uploadUrl === undefined) {\r\n                this._createResumable();\r\n            }\r\n            else {\r\n                if (this._needToFetchStatus) {\r\n                    this._fetchStatus();\r\n                }\r\n                else {\r\n                    if (this._needToFetchMetadata) {\r\n                        // Happens if we miss the metadata on upload completion.\r\n                        this._fetchMetadata();\r\n                    }\r\n                    else {\r\n                        this.pendingTimeout = setTimeout(() => {\r\n                            this.pendingTimeout = undefined;\r\n                            this._continueUpload();\r\n                        }, this.sleepTime);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            this._oneShotUpload();\r\n        }\r\n    }\r\n    _resolveToken(callback) {\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        Promise.all([\r\n            this._ref.storage._getAuthToken(),\r\n            this._ref.storage._getAppCheckToken()\r\n        ]).then(([authToken, appCheckToken]) => {\r\n            switch (this._state) {\r\n                case \"running\" /* InternalTaskState.RUNNING */:\r\n                    callback(authToken, appCheckToken);\r\n                    break;\r\n                case \"canceling\" /* InternalTaskState.CANCELING */:\r\n                    this._transition(\"canceled\" /* InternalTaskState.CANCELED */);\r\n                    break;\r\n                case \"pausing\" /* InternalTaskState.PAUSING */:\r\n                    this._transition(\"paused\" /* InternalTaskState.PAUSED */);\r\n                    break;\r\n            }\r\n        });\r\n    }\r\n    // TODO(andysoto): assert false\r\n    _createResumable() {\r\n        this._resolveToken((authToken, appCheckToken) => {\r\n            const requestInfo = createResumableUpload(this._ref.storage, this._ref._location, this._mappings, this._blob, this._metadata);\r\n            const createRequest = this._ref.storage._makeRequest(requestInfo, newTextConnection, authToken, appCheckToken);\r\n            this._request = createRequest;\r\n            createRequest.getPromise().then((url) => {\r\n                this._request = undefined;\r\n                this._uploadUrl = url;\r\n                this._needToFetchStatus = false;\r\n                this.completeTransitions_();\r\n            }, this._errorHandler);\r\n        });\r\n    }\r\n    _fetchStatus() {\r\n        // TODO(andysoto): assert(this.uploadUrl_ !== null);\r\n        const url = this._uploadUrl;\r\n        this._resolveToken((authToken, appCheckToken) => {\r\n            const requestInfo = getResumableUploadStatus(this._ref.storage, this._ref._location, url, this._blob);\r\n            const statusRequest = this._ref.storage._makeRequest(requestInfo, newTextConnection, authToken, appCheckToken);\r\n            this._request = statusRequest;\r\n            statusRequest.getPromise().then(status => {\r\n                status = status;\r\n                this._request = undefined;\r\n                this._updateProgress(status.current);\r\n                this._needToFetchStatus = false;\r\n                if (status.finalized) {\r\n                    this._needToFetchMetadata = true;\r\n                }\r\n                this.completeTransitions_();\r\n            }, this._errorHandler);\r\n        });\r\n    }\r\n    _continueUpload() {\r\n        const chunkSize = RESUMABLE_UPLOAD_CHUNK_SIZE * this._chunkMultiplier;\r\n        const status = new ResumableUploadStatus(this._transferred, this._blob.size());\r\n        // TODO(andysoto): assert(this.uploadUrl_ !== null);\r\n        const url = this._uploadUrl;\r\n        this._resolveToken((authToken, appCheckToken) => {\r\n            let requestInfo;\r\n            try {\r\n                requestInfo = continueResumableUpload(this._ref._location, this._ref.storage, url, this._blob, chunkSize, this._mappings, status, this._makeProgressCallback());\r\n            }\r\n            catch (e) {\r\n                this._error = e;\r\n                this._transition(\"error\" /* InternalTaskState.ERROR */);\r\n                return;\r\n            }\r\n            const uploadRequest = this._ref.storage._makeRequest(requestInfo, newTextConnection, authToken, appCheckToken, \r\n            /*retry=*/ false // Upload requests should not be retried as each retry should be preceded by another query request. Which is handled in this file.\r\n            );\r\n            this._request = uploadRequest;\r\n            uploadRequest.getPromise().then((newStatus) => {\r\n                this._increaseMultiplier();\r\n                this._request = undefined;\r\n                this._updateProgress(newStatus.current);\r\n                if (newStatus.finalized) {\r\n                    this._metadata = newStatus.metadata;\r\n                    this._transition(\"success\" /* InternalTaskState.SUCCESS */);\r\n                }\r\n                else {\r\n                    this.completeTransitions_();\r\n                }\r\n            }, this._errorHandler);\r\n        });\r\n    }\r\n    _increaseMultiplier() {\r\n        const currentSize = RESUMABLE_UPLOAD_CHUNK_SIZE * this._chunkMultiplier;\r\n        // Max chunk size is 32M.\r\n        if (currentSize * 2 < 32 * 1024 * 1024) {\r\n            this._chunkMultiplier *= 2;\r\n        }\r\n    }\r\n    _fetchMetadata() {\r\n        this._resolveToken((authToken, appCheckToken) => {\r\n            const requestInfo = getMetadata$2(this._ref.storage, this._ref._location, this._mappings);\r\n            const metadataRequest = this._ref.storage._makeRequest(requestInfo, newTextConnection, authToken, appCheckToken);\r\n            this._request = metadataRequest;\r\n            metadataRequest.getPromise().then(metadata => {\r\n                this._request = undefined;\r\n                this._metadata = metadata;\r\n                this._transition(\"success\" /* InternalTaskState.SUCCESS */);\r\n            }, this._metadataErrorHandler);\r\n        });\r\n    }\r\n    _oneShotUpload() {\r\n        this._resolveToken((authToken, appCheckToken) => {\r\n            const requestInfo = multipartUpload(this._ref.storage, this._ref._location, this._mappings, this._blob, this._metadata);\r\n            const multipartRequest = this._ref.storage._makeRequest(requestInfo, newTextConnection, authToken, appCheckToken);\r\n            this._request = multipartRequest;\r\n            multipartRequest.getPromise().then(metadata => {\r\n                this._request = undefined;\r\n                this._metadata = metadata;\r\n                this._updateProgress(this._blob.size());\r\n                this._transition(\"success\" /* InternalTaskState.SUCCESS */);\r\n            }, this._errorHandler);\r\n        });\r\n    }\r\n    _updateProgress(transferred) {\r\n        const old = this._transferred;\r\n        this._transferred = transferred;\r\n        // A progress update can make the \"transferred\" value smaller (e.g. a\r\n        // partial upload not completed by server, after which the \"transferred\"\r\n        // value may reset to the value at the beginning of the request).\r\n        if (this._transferred !== old) {\r\n            this._notifyObservers();\r\n        }\r\n    }\r\n    _transition(state) {\r\n        if (this._state === state) {\r\n            return;\r\n        }\r\n        switch (state) {\r\n            case \"canceling\" /* InternalTaskState.CANCELING */:\r\n            case \"pausing\" /* InternalTaskState.PAUSING */:\r\n                // TODO(andysoto):\r\n                // assert(this.state_ === InternalTaskState.RUNNING ||\r\n                //        this.state_ === InternalTaskState.PAUSING);\r\n                this._state = state;\r\n                if (this._request !== undefined) {\r\n                    this._request.cancel();\r\n                }\r\n                else if (this.pendingTimeout) {\r\n                    clearTimeout(this.pendingTimeout);\r\n                    this.pendingTimeout = undefined;\r\n                    this.completeTransitions_();\r\n                }\r\n                break;\r\n            case \"running\" /* InternalTaskState.RUNNING */:\r\n                // TODO(andysoto):\r\n                // assert(this.state_ === InternalTaskState.PAUSED ||\r\n                //        this.state_ === InternalTaskState.PAUSING);\r\n                const wasPaused = this._state === \"paused\" /* InternalTaskState.PAUSED */;\r\n                this._state = state;\r\n                if (wasPaused) {\r\n                    this._notifyObservers();\r\n                    this._start();\r\n                }\r\n                break;\r\n            case \"paused\" /* InternalTaskState.PAUSED */:\r\n                // TODO(andysoto):\r\n                // assert(this.state_ === InternalTaskState.PAUSING);\r\n                this._state = state;\r\n                this._notifyObservers();\r\n                break;\r\n            case \"canceled\" /* InternalTaskState.CANCELED */:\r\n                // TODO(andysoto):\r\n                // assert(this.state_ === InternalTaskState.PAUSED ||\r\n                //        this.state_ === InternalTaskState.CANCELING);\r\n                this._error = canceled();\r\n                this._state = state;\r\n                this._notifyObservers();\r\n                break;\r\n            case \"error\" /* InternalTaskState.ERROR */:\r\n                // TODO(andysoto):\r\n                // assert(this.state_ === InternalTaskState.RUNNING ||\r\n                //        this.state_ === InternalTaskState.PAUSING ||\r\n                //        this.state_ === InternalTaskState.CANCELING);\r\n                this._state = state;\r\n                this._notifyObservers();\r\n                break;\r\n            case \"success\" /* InternalTaskState.SUCCESS */:\r\n                // TODO(andysoto):\r\n                // assert(this.state_ === InternalTaskState.RUNNING ||\r\n                //        this.state_ === InternalTaskState.PAUSING ||\r\n                //        this.state_ === InternalTaskState.CANCELING);\r\n                this._state = state;\r\n                this._notifyObservers();\r\n                break;\r\n        }\r\n    }\r\n    completeTransitions_() {\r\n        switch (this._state) {\r\n            case \"pausing\" /* InternalTaskState.PAUSING */:\r\n                this._transition(\"paused\" /* InternalTaskState.PAUSED */);\r\n                break;\r\n            case \"canceling\" /* InternalTaskState.CANCELING */:\r\n                this._transition(\"canceled\" /* InternalTaskState.CANCELED */);\r\n                break;\r\n            case \"running\" /* InternalTaskState.RUNNING */:\r\n                this._start();\r\n                break;\r\n        }\r\n    }\r\n    /**\r\n     * A snapshot of the current task state.\r\n     */\r\n    get snapshot() {\r\n        const externalState = taskStateFromInternalTaskState(this._state);\r\n        return {\r\n            bytesTransferred: this._transferred,\r\n            totalBytes: this._blob.size(),\r\n            state: externalState,\r\n            metadata: this._metadata,\r\n            task: this,\r\n            ref: this._ref\r\n        };\r\n    }\r\n    /**\r\n     * Adds a callback for an event.\r\n     * @param type - The type of event to listen for.\r\n     * @param nextOrObserver -\r\n     *     The `next` function, which gets called for each item in\r\n     *     the event stream, or an observer object with some or all of these three\r\n     *     properties (`next`, `error`, `complete`).\r\n     * @param error - A function that gets called with a `StorageError`\r\n     *     if the event stream ends due to an error.\r\n     * @param completed - A function that gets called if the\r\n     *     event stream ends normally.\r\n     * @returns\r\n     *     If only the event argument is passed, returns a function you can use to\r\n     *     add callbacks (see the examples above). If more than just the event\r\n     *     argument is passed, returns a function you can call to unregister the\r\n     *     callbacks.\r\n     */\r\n    on(type, nextOrObserver, error, completed) {\r\n        // Note: `type` isn't being used. Its type is also incorrect. TaskEvent should not be a string.\r\n        const observer = new Observer(nextOrObserver || undefined, error || undefined, completed || undefined);\r\n        this._addObserver(observer);\r\n        return () => {\r\n            this._removeObserver(observer);\r\n        };\r\n    }\r\n    /**\r\n     * This object behaves like a Promise, and resolves with its snapshot data\r\n     * when the upload completes.\r\n     * @param onFulfilled - The fulfillment callback. Promise chaining works as normal.\r\n     * @param onRejected - The rejection callback.\r\n     */\r\n    then(onFulfilled, onRejected) {\r\n        // These casts are needed so that TypeScript can infer the types of the\r\n        // resulting Promise.\r\n        return this._promise.then(onFulfilled, onRejected);\r\n    }\r\n    /**\r\n     * Equivalent to calling `then(null, onRejected)`.\r\n     */\r\n    catch(onRejected) {\r\n        return this.then(null, onRejected);\r\n    }\r\n    /**\r\n     * Adds the given observer.\r\n     */\r\n    _addObserver(observer) {\r\n        this._observers.push(observer);\r\n        this._notifyObserver(observer);\r\n    }\r\n    /**\r\n     * Removes the given observer.\r\n     */\r\n    _removeObserver(observer) {\r\n        const i = this._observers.indexOf(observer);\r\n        if (i !== -1) {\r\n            this._observers.splice(i, 1);\r\n        }\r\n    }\r\n    _notifyObservers() {\r\n        this._finishPromise();\r\n        const observers = this._observers.slice();\r\n        observers.forEach(observer => {\r\n            this._notifyObserver(observer);\r\n        });\r\n    }\r\n    _finishPromise() {\r\n        if (this._resolve !== undefined) {\r\n            let triggered = true;\r\n            switch (taskStateFromInternalTaskState(this._state)) {\r\n                case TaskState.SUCCESS:\r\n                    async(this._resolve.bind(null, this.snapshot))();\r\n                    break;\r\n                case TaskState.CANCELED:\r\n                case TaskState.ERROR:\r\n                    const toCall = this._reject;\r\n                    async(toCall.bind(null, this._error))();\r\n                    break;\r\n                default:\r\n                    triggered = false;\r\n                    break;\r\n            }\r\n            if (triggered) {\r\n                this._resolve = undefined;\r\n                this._reject = undefined;\r\n            }\r\n        }\r\n    }\r\n    _notifyObserver(observer) {\r\n        const externalState = taskStateFromInternalTaskState(this._state);\r\n        switch (externalState) {\r\n            case TaskState.RUNNING:\r\n            case TaskState.PAUSED:\r\n                if (observer.next) {\r\n                    async(observer.next.bind(observer, this.snapshot))();\r\n                }\r\n                break;\r\n            case TaskState.SUCCESS:\r\n                if (observer.complete) {\r\n                    async(observer.complete.bind(observer))();\r\n                }\r\n                break;\r\n            case TaskState.CANCELED:\r\n            case TaskState.ERROR:\r\n                if (observer.error) {\r\n                    async(observer.error.bind(observer, this._error))();\r\n                }\r\n                break;\r\n            default:\r\n                // TODO(andysoto): assert(false);\r\n                if (observer.error) {\r\n                    async(observer.error.bind(observer, this._error))();\r\n                }\r\n        }\r\n    }\r\n    /**\r\n     * Resumes a paused task. Has no effect on a currently running or failed task.\r\n     * @returns True if the operation took effect, false if ignored.\r\n     */\r\n    resume() {\r\n        const valid = this._state === \"paused\" /* InternalTaskState.PAUSED */ ||\r\n            this._state === \"pausing\" /* InternalTaskState.PAUSING */;\r\n        if (valid) {\r\n            this._transition(\"running\" /* InternalTaskState.RUNNING */);\r\n        }\r\n        return valid;\r\n    }\r\n    /**\r\n     * Pauses a currently running task. Has no effect on a paused or failed task.\r\n     * @returns True if the operation took effect, false if ignored.\r\n     */\r\n    pause() {\r\n        const valid = this._state === \"running\" /* InternalTaskState.RUNNING */;\r\n        if (valid) {\r\n            this._transition(\"pausing\" /* InternalTaskState.PAUSING */);\r\n        }\r\n        return valid;\r\n    }\r\n    /**\r\n     * Cancels a currently running or paused task. Has no effect on a complete or\r\n     * failed task.\r\n     * @returns True if the operation took effect, false if ignored.\r\n     */\r\n    cancel() {\r\n        const valid = this._state === \"running\" /* InternalTaskState.RUNNING */ ||\r\n            this._state === \"pausing\" /* InternalTaskState.PAUSING */;\r\n        if (valid) {\r\n            this._transition(\"canceling\" /* InternalTaskState.CANCELING */);\r\n        }\r\n        return valid;\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Provides methods to interact with a bucket in the Firebase Storage service.\r\n * @internal\r\n * @param _location - An fbs.location, or the URL at\r\n *     which to base this object, in one of the following forms:\r\n *         gs://<bucket>/<object-path>\r\n *         http[s]://firebasestorage.googleapis.com/\r\n *                     <api-version>/b/<bucket>/o/<object-path>\r\n *     Any query or fragment strings will be ignored in the http[s]\r\n *     format. If no value is passed, the storage object will use a URL based on\r\n *     the project ID of the base firebase.App instance.\r\n */\r\nclass Reference {\r\n    constructor(_service, location) {\r\n        this._service = _service;\r\n        if (location instanceof Location) {\r\n            this._location = location;\r\n        }\r\n        else {\r\n            this._location = Location.makeFromUrl(location, _service.host);\r\n        }\r\n    }\r\n    /**\r\n     * Returns the URL for the bucket and path this object references,\r\n     *     in the form gs://<bucket>/<object-path>\r\n     * @override\r\n     */\r\n    toString() {\r\n        return 'gs://' + this._location.bucket + '/' + this._location.path;\r\n    }\r\n    _newRef(service, location) {\r\n        return new Reference(service, location);\r\n    }\r\n    /**\r\n     * A reference to the root of this object's bucket.\r\n     */\r\n    get root() {\r\n        const location = new Location(this._location.bucket, '');\r\n        return this._newRef(this._service, location);\r\n    }\r\n    /**\r\n     * The name of the bucket containing this reference's object.\r\n     */\r\n    get bucket() {\r\n        return this._location.bucket;\r\n    }\r\n    /**\r\n     * The full path of this object.\r\n     */\r\n    get fullPath() {\r\n        return this._location.path;\r\n    }\r\n    /**\r\n     * The short name of this object, which is the last component of the full path.\r\n     * For example, if fullPath is 'full/path/image.png', name is 'image.png'.\r\n     */\r\n    get name() {\r\n        return lastComponent(this._location.path);\r\n    }\r\n    /**\r\n     * The `StorageService` instance this `StorageReference` is associated with.\r\n     */\r\n    get storage() {\r\n        return this._service;\r\n    }\r\n    /**\r\n     * A `StorageReference` pointing to the parent location of this `StorageReference`, or null if\r\n     * this reference is the root.\r\n     */\r\n    get parent() {\r\n        const newPath = parent(this._location.path);\r\n        if (newPath === null) {\r\n            return null;\r\n        }\r\n        const location = new Location(this._location.bucket, newPath);\r\n        return new Reference(this._service, location);\r\n    }\r\n    /**\r\n     * Utility function to throw an error in methods that do not accept a root reference.\r\n     */\r\n    _throwIfRoot(name) {\r\n        if (this._location.path === '') {\r\n            throw invalidRootOperation(name);\r\n        }\r\n    }\r\n}\r\n/**\r\n * Download the bytes at the object's location.\r\n * @returns A Promise containing the downloaded bytes.\r\n */\r\nfunction getBytesInternal(ref, maxDownloadSizeBytes) {\r\n    ref._throwIfRoot('getBytes');\r\n    const requestInfo = getBytes$1(ref.storage, ref._location, maxDownloadSizeBytes);\r\n    return ref.storage\r\n        .makeRequestWithTokens(requestInfo, newBytesConnection)\r\n        .then(bytes => maxDownloadSizeBytes !== undefined\r\n        ? // GCS may not honor the Range header for small files\r\n            bytes.slice(0, maxDownloadSizeBytes)\r\n        : bytes);\r\n}\r\n/** Stream the bytes at the object's location. */\r\nfunction getStreamInternal(ref, maxDownloadSizeBytes) {\r\n    ref._throwIfRoot('getStream');\r\n    const requestInfo = getBytes$1(ref.storage, ref._location, maxDownloadSizeBytes);\r\n    // Transforms the stream so that only `maxDownloadSizeBytes` bytes are piped to the result\r\n    const newMaxSizeTransform = (n) => {\r\n        let missingBytes = n;\r\n        return {\r\n            transform(chunk, controller) {\r\n                // GCS may not honor the Range header for small files\r\n                if (chunk.length < missingBytes) {\r\n                    controller.enqueue(chunk);\r\n                    missingBytes -= chunk.length;\r\n                }\r\n                else {\r\n                    controller.enqueue(chunk.slice(0, missingBytes));\r\n                    controller.terminate();\r\n                }\r\n            }\r\n        };\r\n    };\r\n    const result = maxDownloadSizeBytes !== undefined\r\n        ? new TransformStream(newMaxSizeTransform(maxDownloadSizeBytes))\r\n        : new TransformStream(); // The default transformer forwards all chunks to its readable side\r\n    ref.storage\r\n        .makeRequestWithTokens(requestInfo, newStreamConnection)\r\n        .then(readableStream => readableStream.pipeThrough(result))\r\n        .catch(err => result.writable.abort(err));\r\n    return result.readable;\r\n}\r\n/**\r\n * Uploads data to this object's location.\r\n * The upload is not resumable.\r\n *\r\n * @param ref - StorageReference where data should be uploaded.\r\n * @param data - The data to upload.\r\n * @param metadata - Metadata for the newly uploaded data.\r\n * @returns A Promise containing an UploadResult\r\n */\r\nfunction uploadBytes$1(ref, data, metadata) {\r\n    ref._throwIfRoot('uploadBytes');\r\n    const requestInfo = multipartUpload(ref.storage, ref._location, getMappings(), new FbsBlob(data, true), metadata);\r\n    return ref.storage\r\n        .makeRequestWithTokens(requestInfo, newTextConnection)\r\n        .then(finalMetadata => {\r\n        return {\r\n            metadata: finalMetadata,\r\n            ref\r\n        };\r\n    });\r\n}\r\n/**\r\n * Uploads data to this object's location.\r\n * The upload can be paused and resumed, and exposes progress updates.\r\n * @public\r\n * @param ref - StorageReference where data should be uploaded.\r\n * @param data - The data to upload.\r\n * @param metadata - Metadata for the newly uploaded data.\r\n * @returns An UploadTask\r\n */\r\nfunction uploadBytesResumable$1(ref, data, metadata) {\r\n    ref._throwIfRoot('uploadBytesResumable');\r\n    return new UploadTask(ref, new FbsBlob(data), metadata);\r\n}\r\n/**\r\n * Uploads a string to this object's location.\r\n * The upload is not resumable.\r\n * @public\r\n * @param ref - StorageReference where string should be uploaded.\r\n * @param value - The string to upload.\r\n * @param format - The format of the string to upload.\r\n * @param metadata - Metadata for the newly uploaded string.\r\n * @returns A Promise containing an UploadResult\r\n */\r\nfunction uploadString$1(ref, value, format = StringFormat.RAW, metadata) {\r\n    ref._throwIfRoot('uploadString');\r\n    const data = dataFromString(format, value);\r\n    const metadataClone = Object.assign({}, metadata);\r\n    if (metadataClone['contentType'] == null && data.contentType != null) {\r\n        metadataClone['contentType'] = data.contentType;\r\n    }\r\n    return uploadBytes$1(ref, data.data, metadataClone);\r\n}\r\n/**\r\n * List all items (files) and prefixes (folders) under this storage reference.\r\n *\r\n * This is a helper method for calling list() repeatedly until there are\r\n * no more results. The default pagination size is 1000.\r\n *\r\n * Note: The results may not be consistent if objects are changed while this\r\n * operation is running.\r\n *\r\n * Warning: listAll may potentially consume too many resources if there are\r\n * too many results.\r\n * @public\r\n * @param ref - StorageReference to get list from.\r\n *\r\n * @returns A Promise that resolves with all the items and prefixes under\r\n *      the current storage reference. `prefixes` contains references to\r\n *      sub-directories and `items` contains references to objects in this\r\n *      folder. `nextPageToken` is never returned.\r\n */\r\nfunction listAll$1(ref) {\r\n    const accumulator = {\r\n        prefixes: [],\r\n        items: []\r\n    };\r\n    return listAllHelper(ref, accumulator).then(() => accumulator);\r\n}\r\n/**\r\n * Separated from listAll because async functions can't use \"arguments\".\r\n * @param ref\r\n * @param accumulator\r\n * @param pageToken\r\n */\r\nasync function listAllHelper(ref, accumulator, pageToken) {\r\n    const opt = {\r\n        // maxResults is 1000 by default.\r\n        pageToken\r\n    };\r\n    const nextPage = await list$1(ref, opt);\r\n    accumulator.prefixes.push(...nextPage.prefixes);\r\n    accumulator.items.push(...nextPage.items);\r\n    if (nextPage.nextPageToken != null) {\r\n        await listAllHelper(ref, accumulator, nextPage.nextPageToken);\r\n    }\r\n}\r\n/**\r\n * List items (files) and prefixes (folders) under this storage reference.\r\n *\r\n * List API is only available for Firebase Rules Version 2.\r\n *\r\n * GCS is a key-blob store. Firebase Storage imposes the semantic of '/'\r\n * delimited folder structure.\r\n * Refer to GCS's List API if you want to learn more.\r\n *\r\n * To adhere to Firebase Rules's Semantics, Firebase Storage does not\r\n * support objects whose paths end with \"/\" or contain two consecutive\r\n * \"/\"s. Firebase Storage List API will filter these unsupported objects.\r\n * list() may fail if there are too many unsupported objects in the bucket.\r\n * @public\r\n *\r\n * @param ref - StorageReference to get list from.\r\n * @param options - See ListOptions for details.\r\n * @returns A Promise that resolves with the items and prefixes.\r\n *      `prefixes` contains references to sub-folders and `items`\r\n *      contains references to objects in this folder. `nextPageToken`\r\n *      can be used to get the rest of the results.\r\n */\r\nfunction list$1(ref, options) {\r\n    if (options != null) {\r\n        if (typeof options.maxResults === 'number') {\r\n            validateNumber('options.maxResults', \r\n            /* minValue= */ 1, \r\n            /* maxValue= */ 1000, options.maxResults);\r\n        }\r\n    }\r\n    const op = options || {};\r\n    const requestInfo = list$2(ref.storage, ref._location, \r\n    /*delimiter= */ '/', op.pageToken, op.maxResults);\r\n    return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\r\n}\r\n/**\r\n * A `Promise` that resolves with the metadata for this object. If this\r\n * object doesn't exist or metadata cannot be retrieved, the promise is\r\n * rejected.\r\n * @public\r\n * @param ref - StorageReference to get metadata from.\r\n */\r\nfunction getMetadata$1(ref) {\r\n    ref._throwIfRoot('getMetadata');\r\n    const requestInfo = getMetadata$2(ref.storage, ref._location, getMappings());\r\n    return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\r\n}\r\n/**\r\n * Updates the metadata for this object.\r\n * @public\r\n * @param ref - StorageReference to update metadata for.\r\n * @param metadata - The new metadata for the object.\r\n *     Only values that have been explicitly set will be changed. Explicitly\r\n *     setting a value to null will remove the metadata.\r\n * @returns A `Promise` that resolves\r\n *     with the new metadata for this object.\r\n *     See `firebaseStorage.Reference.prototype.getMetadata`\r\n */\r\nfunction updateMetadata$1(ref, metadata) {\r\n    ref._throwIfRoot('updateMetadata');\r\n    const requestInfo = updateMetadata$2(ref.storage, ref._location, metadata, getMappings());\r\n    return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\r\n}\r\n/**\r\n * Returns the download URL for the given Reference.\r\n * @public\r\n * @returns A `Promise` that resolves with the download\r\n *     URL for this object.\r\n */\r\nfunction getDownloadURL$1(ref) {\r\n    ref._throwIfRoot('getDownloadURL');\r\n    const requestInfo = getDownloadUrl(ref.storage, ref._location, getMappings());\r\n    return ref.storage\r\n        .makeRequestWithTokens(requestInfo, newTextConnection)\r\n        .then(url => {\r\n        if (url === null) {\r\n            throw noDownloadURL();\r\n        }\r\n        return url;\r\n    });\r\n}\r\n/**\r\n * Deletes the object at this location.\r\n * @public\r\n * @param ref - StorageReference for object to delete.\r\n * @returns A `Promise` that resolves if the deletion succeeds.\r\n */\r\nfunction deleteObject$1(ref) {\r\n    ref._throwIfRoot('deleteObject');\r\n    const requestInfo = deleteObject$2(ref.storage, ref._location);\r\n    return ref.storage.makeRequestWithTokens(requestInfo, newTextConnection);\r\n}\r\n/**\r\n * Returns reference for object obtained by appending `childPath` to `ref`.\r\n *\r\n * @param ref - StorageReference to get child of.\r\n * @param childPath - Child path from provided ref.\r\n * @returns A reference to the object obtained by\r\n * appending childPath, removing any duplicate, beginning, or trailing\r\n * slashes.\r\n *\r\n */\r\nfunction _getChild$1(ref, childPath) {\r\n    const newPath = child(ref._location.path, childPath);\r\n    const location = new Location(ref._location.bucket, newPath);\r\n    return new Reference(ref.storage, location);\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction isUrl(path) {\r\n    return /^[A-Za-z]+:\\/\\//.test(path);\r\n}\r\n/**\r\n * Returns a firebaseStorage.Reference for the given url.\r\n */\r\nfunction refFromURL(service, url) {\r\n    return new Reference(service, url);\r\n}\r\n/**\r\n * Returns a firebaseStorage.Reference for the given path in the default\r\n * bucket.\r\n */\r\nfunction refFromPath(ref, path) {\r\n    if (ref instanceof FirebaseStorageImpl) {\r\n        const service = ref;\r\n        if (service._bucket == null) {\r\n            throw noDefaultBucket();\r\n        }\r\n        const reference = new Reference(service, service._bucket);\r\n        if (path != null) {\r\n            return refFromPath(reference, path);\r\n        }\r\n        else {\r\n            return reference;\r\n        }\r\n    }\r\n    else {\r\n        // ref is a Reference\r\n        if (path !== undefined) {\r\n            return _getChild$1(ref, path);\r\n        }\r\n        else {\r\n            return ref;\r\n        }\r\n    }\r\n}\r\nfunction ref$1(serviceOrRef, pathOrUrl) {\r\n    if (pathOrUrl && isUrl(pathOrUrl)) {\r\n        if (serviceOrRef instanceof FirebaseStorageImpl) {\r\n            return refFromURL(serviceOrRef, pathOrUrl);\r\n        }\r\n        else {\r\n            throw invalidArgument('To use ref(service, url), the first argument must be a Storage instance.');\r\n        }\r\n    }\r\n    else {\r\n        return refFromPath(serviceOrRef, pathOrUrl);\r\n    }\r\n}\r\nfunction extractBucket(host, config) {\r\n    const bucketString = config === null || config === void 0 ? void 0 : config[CONFIG_STORAGE_BUCKET_KEY];\r\n    if (bucketString == null) {\r\n        return null;\r\n    }\r\n    return Location.makeFromBucketSpec(bucketString, host);\r\n}\r\nfunction connectStorageEmulator$1(storage, host, port, options = {}) {\r\n    storage.host = `${host}:${port}`;\r\n    storage._protocol = 'http';\r\n    const { mockUserToken } = options;\r\n    if (mockUserToken) {\r\n        storage._overrideAuthToken =\r\n            typeof mockUserToken === 'string'\r\n                ? mockUserToken\r\n                : (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.createMockUserToken)(mockUserToken, storage.app.options.projectId);\r\n    }\r\n}\r\n/**\r\n * A service that provides Firebase Storage Reference instances.\r\n * @param opt_url - gs:// url to a custom Storage Bucket\r\n *\r\n * @internal\r\n */\r\nclass FirebaseStorageImpl {\r\n    constructor(\r\n    /**\r\n     * FirebaseApp associated with this StorageService instance.\r\n     */\r\n    app, _authProvider, \r\n    /**\r\n     * @internal\r\n     */\r\n    _appCheckProvider, \r\n    /**\r\n     * @internal\r\n     */\r\n    _url, _firebaseVersion) {\r\n        this.app = app;\r\n        this._authProvider = _authProvider;\r\n        this._appCheckProvider = _appCheckProvider;\r\n        this._url = _url;\r\n        this._firebaseVersion = _firebaseVersion;\r\n        this._bucket = null;\r\n        /**\r\n         * This string can be in the formats:\r\n         * - host\r\n         * - host:port\r\n         */\r\n        this._host = DEFAULT_HOST;\r\n        this._protocol = 'https';\r\n        this._appId = null;\r\n        this._deleted = false;\r\n        this._maxOperationRetryTime = DEFAULT_MAX_OPERATION_RETRY_TIME;\r\n        this._maxUploadRetryTime = DEFAULT_MAX_UPLOAD_RETRY_TIME;\r\n        this._requests = new Set();\r\n        if (_url != null) {\r\n            this._bucket = Location.makeFromBucketSpec(_url, this._host);\r\n        }\r\n        else {\r\n            this._bucket = extractBucket(this._host, this.app.options);\r\n        }\r\n    }\r\n    /**\r\n     * The host string for this service, in the form of `host` or\r\n     * `host:port`.\r\n     */\r\n    get host() {\r\n        return this._host;\r\n    }\r\n    set host(host) {\r\n        this._host = host;\r\n        if (this._url != null) {\r\n            this._bucket = Location.makeFromBucketSpec(this._url, host);\r\n        }\r\n        else {\r\n            this._bucket = extractBucket(host, this.app.options);\r\n        }\r\n    }\r\n    /**\r\n     * The maximum time to retry uploads in milliseconds.\r\n     */\r\n    get maxUploadRetryTime() {\r\n        return this._maxUploadRetryTime;\r\n    }\r\n    set maxUploadRetryTime(time) {\r\n        validateNumber('time', \r\n        /* minValue=*/ 0, \r\n        /* maxValue= */ Number.POSITIVE_INFINITY, time);\r\n        this._maxUploadRetryTime = time;\r\n    }\r\n    /**\r\n     * The maximum time to retry operations other than uploads or downloads in\r\n     * milliseconds.\r\n     */\r\n    get maxOperationRetryTime() {\r\n        return this._maxOperationRetryTime;\r\n    }\r\n    set maxOperationRetryTime(time) {\r\n        validateNumber('time', \r\n        /* minValue=*/ 0, \r\n        /* maxValue= */ Number.POSITIVE_INFINITY, time);\r\n        this._maxOperationRetryTime = time;\r\n    }\r\n    async _getAuthToken() {\r\n        if (this._overrideAuthToken) {\r\n            return this._overrideAuthToken;\r\n        }\r\n        const auth = this._authProvider.getImmediate({ optional: true });\r\n        if (auth) {\r\n            const tokenData = await auth.getToken();\r\n            if (tokenData !== null) {\r\n                return tokenData.accessToken;\r\n            }\r\n        }\r\n        return null;\r\n    }\r\n    async _getAppCheckToken() {\r\n        const appCheck = this._appCheckProvider.getImmediate({ optional: true });\r\n        if (appCheck) {\r\n            const result = await appCheck.getToken();\r\n            // TODO: What do we want to do if there is an error getting the token?\r\n            // Context: appCheck.getToken() will never throw even if an error happened. In the error case, a dummy token will be\r\n            // returned along with an error field describing the error. In general, we shouldn't care about the error condition and just use\r\n            // the token (actual or dummy) to send requests.\r\n            return result.token;\r\n        }\r\n        return null;\r\n    }\r\n    /**\r\n     * Stop running requests and prevent more from being created.\r\n     */\r\n    _delete() {\r\n        if (!this._deleted) {\r\n            this._deleted = true;\r\n            this._requests.forEach(request => request.cancel());\r\n            this._requests.clear();\r\n        }\r\n        return Promise.resolve();\r\n    }\r\n    /**\r\n     * Returns a new firebaseStorage.Reference object referencing this StorageService\r\n     * at the given Location.\r\n     */\r\n    _makeStorageReference(loc) {\r\n        return new Reference(this, loc);\r\n    }\r\n    /**\r\n     * @param requestInfo - HTTP RequestInfo object\r\n     * @param authToken - Firebase auth token\r\n     */\r\n    _makeRequest(requestInfo, requestFactory, authToken, appCheckToken, retry = true) {\r\n        if (!this._deleted) {\r\n            const request = makeRequest(requestInfo, this._appId, authToken, appCheckToken, requestFactory, this._firebaseVersion, retry);\r\n            this._requests.add(request);\r\n            // Request removes itself from set when complete.\r\n            request.getPromise().then(() => this._requests.delete(request), () => this._requests.delete(request));\r\n            return request;\r\n        }\r\n        else {\r\n            return new FailRequest(appDeleted());\r\n        }\r\n    }\r\n    async makeRequestWithTokens(requestInfo, requestFactory) {\r\n        const [authToken, appCheckToken] = await Promise.all([\r\n            this._getAuthToken(),\r\n            this._getAppCheckToken()\r\n        ]);\r\n        return this._makeRequest(requestInfo, requestFactory, authToken, appCheckToken).getPromise();\r\n    }\r\n}\n\nconst name = \"@firebase/storage\";\nconst version = \"0.13.2\";\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Type constant for Firebase Storage.\r\n */\r\nconst STORAGE_TYPE = 'storage';\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Downloads the data at the object's location. Returns an error if the object\r\n * is not found.\r\n *\r\n * To use this functionality, you have to whitelist your app's origin in your\r\n * Cloud Storage bucket. See also\r\n * https://cloud.google.com/storage/docs/configuring-cors\r\n *\r\n * @public\r\n * @param ref - StorageReference where data should be downloaded.\r\n * @param maxDownloadSizeBytes - If set, the maximum allowed size in bytes to\r\n * retrieve.\r\n * @returns A Promise containing the object's bytes\r\n */\r\nfunction getBytes(ref, maxDownloadSizeBytes) {\r\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\r\n    return getBytesInternal(ref, maxDownloadSizeBytes);\r\n}\r\n/**\r\n * Uploads data to this object's location.\r\n * The upload is not resumable.\r\n * @public\r\n * @param ref - {@link StorageReference} where data should be uploaded.\r\n * @param data - The data to upload.\r\n * @param metadata - Metadata for the data to upload.\r\n * @returns A Promise containing an UploadResult\r\n */\r\nfunction uploadBytes(ref, data, metadata) {\r\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\r\n    return uploadBytes$1(ref, data, metadata);\r\n}\r\n/**\r\n * Uploads a string to this object's location.\r\n * The upload is not resumable.\r\n * @public\r\n * @param ref - {@link StorageReference} where string should be uploaded.\r\n * @param value - The string to upload.\r\n * @param format - The format of the string to upload.\r\n * @param metadata - Metadata for the string to upload.\r\n * @returns A Promise containing an UploadResult\r\n */\r\nfunction uploadString(ref, value, format, metadata) {\r\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\r\n    return uploadString$1(ref, value, format, metadata);\r\n}\r\n/**\r\n * Uploads data to this object's location.\r\n * The upload can be paused and resumed, and exposes progress updates.\r\n * @public\r\n * @param ref - {@link StorageReference} where data should be uploaded.\r\n * @param data - The data to upload.\r\n * @param metadata - Metadata for the data to upload.\r\n * @returns An UploadTask\r\n */\r\nfunction uploadBytesResumable(ref, data, metadata) {\r\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\r\n    return uploadBytesResumable$1(ref, data, metadata);\r\n}\r\n/**\r\n * A `Promise` that resolves with the metadata for this object. If this\r\n * object doesn't exist or metadata cannot be retrieved, the promise is\r\n * rejected.\r\n * @public\r\n * @param ref - {@link StorageReference} to get metadata from.\r\n */\r\nfunction getMetadata(ref) {\r\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\r\n    return getMetadata$1(ref);\r\n}\r\n/**\r\n * Updates the metadata for this object.\r\n * @public\r\n * @param ref - {@link StorageReference} to update metadata for.\r\n * @param metadata - The new metadata for the object.\r\n *     Only values that have been explicitly set will be changed. Explicitly\r\n *     setting a value to null will remove the metadata.\r\n * @returns A `Promise` that resolves with the new metadata for this object.\r\n */\r\nfunction updateMetadata(ref, metadata) {\r\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\r\n    return updateMetadata$1(ref, metadata);\r\n}\r\n/**\r\n * List items (files) and prefixes (folders) under this storage reference.\r\n *\r\n * List API is only available for Firebase Rules Version 2.\r\n *\r\n * GCS is a key-blob store. Firebase Storage imposes the semantic of '/'\r\n * delimited folder structure.\r\n * Refer to GCS's List API if you want to learn more.\r\n *\r\n * To adhere to Firebase Rules's Semantics, Firebase Storage does not\r\n * support objects whose paths end with \"/\" or contain two consecutive\r\n * \"/\"s. Firebase Storage List API will filter these unsupported objects.\r\n * list() may fail if there are too many unsupported objects in the bucket.\r\n * @public\r\n *\r\n * @param ref - {@link StorageReference} to get list from.\r\n * @param options - See {@link ListOptions} for details.\r\n * @returns A `Promise` that resolves with the items and prefixes.\r\n *      `prefixes` contains references to sub-folders and `items`\r\n *      contains references to objects in this folder. `nextPageToken`\r\n *      can be used to get the rest of the results.\r\n */\r\nfunction list(ref, options) {\r\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\r\n    return list$1(ref, options);\r\n}\r\n/**\r\n * List all items (files) and prefixes (folders) under this storage reference.\r\n *\r\n * This is a helper method for calling list() repeatedly until there are\r\n * no more results. The default pagination size is 1000.\r\n *\r\n * Note: The results may not be consistent if objects are changed while this\r\n * operation is running.\r\n *\r\n * Warning: `listAll` may potentially consume too many resources if there are\r\n * too many results.\r\n * @public\r\n * @param ref - {@link StorageReference} to get list from.\r\n *\r\n * @returns A `Promise` that resolves with all the items and prefixes under\r\n *      the current storage reference. `prefixes` contains references to\r\n *      sub-directories and `items` contains references to objects in this\r\n *      folder. `nextPageToken` is never returned.\r\n */\r\nfunction listAll(ref) {\r\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\r\n    return listAll$1(ref);\r\n}\r\n/**\r\n * Returns the download URL for the given {@link StorageReference}.\r\n * @public\r\n * @param ref - {@link StorageReference} to get the download URL for.\r\n * @returns A `Promise` that resolves with the download\r\n *     URL for this object.\r\n */\r\nfunction getDownloadURL(ref) {\r\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\r\n    return getDownloadURL$1(ref);\r\n}\r\n/**\r\n * Deletes the object at this location.\r\n * @public\r\n * @param ref - {@link StorageReference} for object to delete.\r\n * @returns A `Promise` that resolves if the deletion succeeds.\r\n */\r\nfunction deleteObject(ref) {\r\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\r\n    return deleteObject$1(ref);\r\n}\r\nfunction ref(serviceOrRef, pathOrUrl) {\r\n    serviceOrRef = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(serviceOrRef);\r\n    return ref$1(serviceOrRef, pathOrUrl);\r\n}\r\n/**\r\n * @internal\r\n */\r\nfunction _getChild(ref, childPath) {\r\n    return _getChild$1(ref, childPath);\r\n}\r\n/**\r\n * Gets a {@link FirebaseStorage} instance for the given Firebase app.\r\n * @public\r\n * @param app - Firebase app to get {@link FirebaseStorage} instance for.\r\n * @param bucketUrl - The gs:// url to your Firebase Storage Bucket.\r\n * If not passed, uses the app's default Storage Bucket.\r\n * @returns A {@link FirebaseStorage} instance.\r\n */\r\nfunction getStorage(app = (0,_firebase_app__WEBPACK_IMPORTED_MODULE_0__.getApp)(), bucketUrl) {\r\n    app = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(app);\r\n    const storageProvider = (0,_firebase_app__WEBPACK_IMPORTED_MODULE_0__._getProvider)(app, STORAGE_TYPE);\r\n    const storageInstance = storageProvider.getImmediate({\r\n        identifier: bucketUrl\r\n    });\r\n    const emulator = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getDefaultEmulatorHostnameAndPort)('storage');\r\n    if (emulator) {\r\n        connectStorageEmulator(storageInstance, ...emulator);\r\n    }\r\n    return storageInstance;\r\n}\r\n/**\r\n * Modify this {@link FirebaseStorage} instance to communicate with the Cloud Storage emulator.\r\n *\r\n * @param storage - The {@link FirebaseStorage} instance\r\n * @param host - The emulator host (ex: localhost)\r\n * @param port - The emulator port (ex: 5001)\r\n * @param options - Emulator options. `options.mockUserToken` is the mock auth\r\n * token to use for unit testing Security Rules.\r\n * @public\r\n */\r\nfunction connectStorageEmulator(storage, host, port, options = {}) {\r\n    connectStorageEmulator$1(storage, host, port, options);\r\n}\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Downloads the data at the object's location. Returns an error if the object\r\n * is not found.\r\n *\r\n * To use this functionality, you have to whitelist your app's origin in your\r\n * Cloud Storage bucket. See also\r\n * https://cloud.google.com/storage/docs/configuring-cors\r\n *\r\n * This API is not available in Node.\r\n *\r\n * @public\r\n * @param ref - StorageReference where data should be downloaded.\r\n * @param maxDownloadSizeBytes - If set, the maximum allowed size in bytes to\r\n * retrieve.\r\n * @returns A Promise that resolves with a Blob containing the object's bytes\r\n */\r\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\r\nfunction getBlob(ref, maxDownloadSizeBytes) {\r\n    throw new Error('getBlob() is only available in Browser-like environments');\r\n}\r\n/**\r\n * Downloads the data at the object's location. Raises an error event if the\r\n * object is not found.\r\n *\r\n * This API is only available in Node.\r\n *\r\n * @public\r\n * @param ref - StorageReference where data should be downloaded.\r\n * @param maxDownloadSizeBytes - If set, the maximum allowed size in bytes to\r\n * retrieve.\r\n * @returns A stream with the object's data as bytes\r\n */\r\nfunction getStream(ref, maxDownloadSizeBytes) {\r\n    ref = (0,_firebase_util__WEBPACK_IMPORTED_MODULE_1__.getModularInstance)(ref);\r\n    return getStreamInternal(ref, maxDownloadSizeBytes);\r\n}\n\n/**\r\n * Cloud Storage for Firebase\r\n *\r\n * @packageDocumentation\r\n */\r\nfunction factory(container, { instanceIdentifier: url }) {\r\n    const app = container.getProvider('app').getImmediate();\r\n    const authProvider = container.getProvider('auth-internal');\r\n    const appCheckProvider = container.getProvider('app-check-internal');\r\n    return new FirebaseStorageImpl(app, authProvider, appCheckProvider, url, _firebase_app__WEBPACK_IMPORTED_MODULE_0__.SDK_VERSION);\r\n}\r\nfunction registerStorage() {\r\n    (0,_firebase_app__WEBPACK_IMPORTED_MODULE_0__._registerComponent)(new _firebase_component__WEBPACK_IMPORTED_MODULE_3__.Component(STORAGE_TYPE, factory, \"PUBLIC\" /* ComponentType.PUBLIC */).setMultipleInstances(true));\r\n    (0,_firebase_app__WEBPACK_IMPORTED_MODULE_0__.registerVersion)(name, version);\r\n}\r\nregisterStorage();\n\n\n//# sourceMappingURL=index.node.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@firebase+storage@0.13.2_@firebase+app@0.10.13/node_modules/@firebase/storage/dist/node-esm/index.node.esm.js\n");

/***/ })

};
;