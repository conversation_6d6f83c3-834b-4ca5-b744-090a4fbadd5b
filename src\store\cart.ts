import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { CartItem } from '@/src/types/db';
import { computeDiscount } from '@/src/lib/discount';

interface CartState {
  items: CartItem[];
  addItem: (item: CartItem) => void;
  removeItem: (productId: string) => void;
  updateQuantity: (productId: string, qty: number) => void;
  clearCart: () => void;
  getTotals: () => {
    totalGross: number;
    discountPercent: number;
    totalNet: number;
    itemCount: number;
  };
  getDiscountInfo: () => ReturnType<typeof computeDiscount>;
}

export const useCartStore = create<CartState>()(
  persist(
    (set, get) => ({
      items: [],
      
      addItem: (newItem) => set((state) => {
        const existingItem = state.items.find(item => item.productId === newItem.productId);
        
        if (existingItem) {
          return {
            items: state.items.map(item =>
              item.productId === newItem.productId
                ? { ...item, qty: item.qty + newItem.qty }
                : item
            )
          };
        }
        
        return { items: [...state.items, newItem] };
      }),
      
      removeItem: (productId) => set((state) => ({
        items: state.items.filter(item => item.productId !== productId)
      })),
      
      updateQuantity: (productId, qty) => set((state) => {
        if (qty <= 0) {
          return { items: state.items.filter(item => item.productId !== productId) };
        }
        
        return {
          items: state.items.map(item =>
            item.productId === productId ? { ...item, qty } : item
          )
        };
      }),
      
      clearCart: () => set({ items: [] }),
      
      getTotals: () => {
        const items = get().items;
        const totalGross = items.reduce((sum, item) => sum + (item.price * item.qty), 0);
        const discountInfo = computeDiscount(totalGross);
        
        return {
          totalGross,
          discountPercent: discountInfo.discountPercent,
          totalNet: discountInfo.totalNet,
          itemCount: items.reduce((sum, item) => sum + item.qty, 0)
        };
      },
      
      getDiscountInfo: () => {
        const totalGross = get().items.reduce((sum, item) => sum + (item.price * item.qty), 0);
        return computeDiscount(totalGross);
      }
    }),
    {
      name: 'docorpo-cart'
    }
  )
);
