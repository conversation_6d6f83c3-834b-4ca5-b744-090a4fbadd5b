'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Header } from '@/src/components/Header';
import { Button } from '@/components/ui/button';
import { useAuthStore } from '@/src/store/auth';
import { Package, TrendingUp, ShoppingBag, Zap, Shield, Users, ArrowRight } from 'lucide-react';

export default function Home() {
  const { user } = useAuthStore();
  const router = useRouter();

  // Se o usuário estiver logado, redireciona para produtos
  useEffect(() => {
    if (user) {
      router.push('/produtos');
    }
  }, [user, router]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-yellow-500/5 via-transparent to-yellow-500/5"></div>

      <Header />

      <main className="relative z-10">
        {/* Hero Section */}
        <section className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 bg-clip-text text-transparent">
              Pronto para Começar?
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 mb-12 max-w-3xl mx-auto">
              Junte-se aos fornecedores que já utilizam nossa plataforma
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link href="/login">
                <Button className="w-full sm:w-auto px-8 py-4 text-lg bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-black font-semibold rounded-lg shadow-lg hover:shadow-yellow-500/25 transition-all duration-200">
                  Entrar / Criar Conta
                </Button>
              </Link>

              <Button
                variant="outline"
                className="w-full sm:w-auto px-8 py-4 text-lg border-yellow-500/50 text-yellow-400 hover:bg-yellow-500/10 hover:text-yellow-300 hover:border-yellow-400 transition-all duration-200"
              >
                <Users className="w-5 h-5 mr-2" />
                Quero Ser Parceiro
              </Button>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20 px-4 sm:px-6 lg:px-8 bg-black/20 backdrop-blur-sm">
          <div className="max-w-7xl mx-auto">
            {/* Hero Image Background */}
            <div className="relative mb-16 rounded-2xl overflow-hidden">
              <div className="bg-gradient-to-r from-black/80 via-black/60 to-black/80 absolute inset-0 z-10"></div>
              <div className="bg-gray-800 h-96 flex items-center justify-center relative">
                <div className="text-center z-20 relative px-8">
                  <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
                    Conecte-se diretamente com fornecedores, explore catálogos exclusivos e faça pedidos com facilidade
                  </h2>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Button className="px-8 py-3 bg-gradient-to-r from-yellow-400 to-yellow-500 hover:from-yellow-500 hover:to-yellow-600 text-black font-semibold shadow-lg hover:shadow-yellow-500/25 transition-all duration-200">
                      <Zap className="w-5 h-5 mr-2" />
                      Comece Agora
                    </Button>
                    <Button variant="outline" className="px-8 py-3 border-white/30 text-white hover:bg-white/10 hover:border-white/50 transition-all duration-200">
                      Saiba Mais
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            {/* Features Grid */}
            <div className="grid md:grid-cols-3 gap-8 mb-16">
              <div className="text-center p-8 bg-black/40 rounded-xl border border-yellow-500/20 backdrop-blur-sm">
                <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Package className="w-8 h-8 text-black" />
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">Catálogo Completo</h3>
                <p className="text-gray-300 text-lg">Milhares de produtos à sua disposição</p>
              </div>

              <div className="text-center p-8 bg-black/40 rounded-xl border border-yellow-500/20 backdrop-blur-sm">
                <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-6">
                  <TrendingUp className="w-8 h-8 text-black" />
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">Negociação Direta</h3>
                <p className="text-gray-300 text-lg">Condições personalizadas com fornecedores</p>
              </div>

              <div className="text-center p-8 bg-black/40 rounded-xl border border-yellow-500/20 backdrop-blur-sm">
                <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-6">
                  <ShoppingBag className="w-8 h-8 text-black" />
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">Pedidos Simplificados</h3>
                <p className="text-gray-300 text-lg">Processo rápido e seguro</p>
              </div>
            </div>
          </div>
        </section>

        {/* How it Works Section */}
        <section className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">Como Funciona</h2>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Processo simples e eficiente para seus pedidos atacadistas
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-12">
              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg shadow-yellow-500/25">
                  <Package className="w-10 h-10 text-black" />
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">1. Cadastre-se</h3>
                <p className="text-gray-300 text-lg">
                  Registre sua empresa (PJ ou PF) em poucos minutos
                </p>
              </div>

              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg shadow-yellow-500/25">
                  <Zap className="w-10 h-10 text-black" />
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">2. Explore o Catálogo</h3>
                <p className="text-gray-300 text-lg">
                  Navegue por milhares de produtos organizados por categoria
                </p>
              </div>

              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg shadow-yellow-500/25">
                  <Shield className="w-10 h-10 text-black" />
                </div>
                <h3 className="text-2xl font-bold text-white mb-4">3. Faça Pedidos</h3>
                <p className="text-gray-300 text-lg">
                  Monte sua malinha e negocie direto com fornecedores
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-yellow-500/10 via-yellow-400/5 to-yellow-500/10">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 bg-clip-text text-transparent">
              Comece Hoje Mesmo
            </h2>
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
              Junte-se a milhares de empresas que já utilizam nossa plataforma para otimizar suas compras
            </p>
            <Link href="/login">
              <Button className="px-12 py-4 text-lg bg-gradient-to-r from-yellow-400 to-yellow-500 text-black font-bold hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 shadow-lg hover:shadow-yellow-500/25">
                Criar Conta Grátis
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </Link>
          </div>
        </section>
      </main>
    </div>
  );
}
