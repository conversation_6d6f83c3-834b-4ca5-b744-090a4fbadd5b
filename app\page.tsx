'use client';

import { useEffect, useState } from 'react';
// import { collection, getDocs, query, where, orderBy } from 'firebase/firestore';
// import { db } from '@/src/lib/firebase';
import { Header } from '@/src/components/Header';
// import { ProductCard } from '@/src/components/ProductCard';
import { CategoryChips } from '@/src/components/CategoryChips';
// import { DiscountGauge } from '@/src/components/DiscountGauge';
import { Product, Category } from '@/src/types/db';
import { useAuthStore } from '@/src/store/auth';

export default function Home() {
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const { user } = useAuthStore();

  useEffect(() => {
    // Simular carregamento de dados
    setTimeout(() => {
      setCategories([
        { id: '1', name: 'Categoria 1', order: 1 },
        { id: '2', name: 'Categoria 2', order: 2 }
      ]);
      setProducts([]);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredProducts = selectedCategory
    ? products.filter(product => product.categoryId === selectedCategory)
    : products;

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Banner de Campanha */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-8 rounded-lg mb-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">
              Bem-vindo ao doCorpo
            </h1>
            <p className="text-xl mb-6">
              Produtos premium com descontos progressivos de até 20%
            </p>
            <div className="text-sm opacity-90">
              Compre mais e economize mais: 10% a partir de R$ 1.000 | 15% a partir de R$ 2.000 | 20% a partir de R$ 3.000
            </div>
          </div>
        </div>

        {/* Filtros de Categoria */}
        <div className="mb-8">
          <CategoryChips
            categories={categories}
            selectedCategory={selectedCategory}
            onSelectCategory={setSelectedCategory}
          />
        </div>

        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">
            Aplicação funcionando com Header e CategoryChips! Carregando dados...
          </p>
          <p className="text-sm text-gray-400 mt-2">
            Categorias: {categories.length} | Produtos: {products.length}
          </p>
        </div>
      </main>
    </div>
  );
}
