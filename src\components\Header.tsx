﻿'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ShoppingBag, User, Menu, LogOut, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuthStore } from '@/src/store/auth';
import { useCartStore } from '@/src/store/cart';
import { signOut } from '@/src/lib/auth';
import { toast } from 'sonner';

export function Header() {
  const { user, userData, loading } = useAuthStore();
  const { getTotals } = useCartStore();
  const { itemCount } = getTotals();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const handleSignOut = async () => {
    const { error } = await signOut();
    if (error) {
      toast.error('Erro ao sair: ' + error);
    } else {
      toast.success('Logout realizado com sucesso');
    }
  };

  return (
    <header className="bg-black shadow-lg border-b border-yellow-500/20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3 hover:opacity-80 transition-opacity">
            <div className="relative">
              <Image
                src="/logo.jpg"
                alt="DOCORPO"
                width={38}
                height={38}
                className="w-10 h-10 md:w-10 md:h-10 rounded-full"
                priority
              />
            </div>
            <div className="flex flex-col justify-center">
              <span className="text-xl md:text-2xl font-bold bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 bg-clip-text text-transparent leading-tight">
                DOCORPO
              </span>
              <span className="text-[6.6px] md:text-[7.7px] text-gray-400 italic leading-tight -mt-1">
                peças que se moldam ao seu ritmo.
              </span>
            </div>
          </Link>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/" className="text-gray-300 hover:text-yellow-400 transition-colors font-medium">
              Início
            </Link>
            {user && (
              <>
                <Link href="/produtos" className="text-gray-300 hover:text-yellow-400 transition-colors font-medium">
                  Produtos
                </Link>
                <Link href="/pedidos" className="text-gray-300 hover:text-yellow-400 transition-colors font-medium">
                  Meus Pedidos
                </Link>
                <Link href="/mensagens" className="text-gray-300 hover:text-yellow-400 transition-colors font-medium">
                  Mensagens
                </Link>
                {userData?.role === 'admin' && (
                  <Link href="/admin" className="text-yellow-400 hover:text-yellow-300 transition-colors font-medium">
                    Admin
                  </Link>
                )}
              </>
            )}
          </nav>

          {/* Right side */}
          <div className="flex items-center space-x-4">
            {/* Mala de Compras */}
            <Link href="/mala" className="relative">
              <Button variant="ghost" size="icon" className="relative text-gray-300 hover:text-yellow-400 hover:bg-gray-800">
                <ShoppingBag className="h-5 w-5" />
                {itemCount > 0 && (
                  <span className="absolute -top-1 -right-1 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold shadow-lg border-2 border-yellow-400 animate-pulse">
                    {itemCount > 99 ? '99+' : itemCount}
                  </span>
                )}
              </Button>
            </Link>

            {/* User menu */}
            {loading ? (
              <div className="w-8 h-8 bg-gray-700 rounded-full animate-pulse" />
            ) : user ? (
              <div className="flex items-center space-x-2">
                <Link href="/perfil">
                  <Button variant="ghost" size="icon" className="text-gray-300 hover:text-yellow-400 hover:bg-gray-800">
                    <User className="h-5 w-5" />
                  </Button>
                </Link>
                <Button variant="ghost" size="icon" onClick={handleSignOut} className="text-gray-300 hover:text-red-400 hover:bg-gray-800">
                  <LogOut className="h-5 w-5" />
                </Button>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Link href="/login">
                  <Button variant="ghost" className="text-gray-300 hover:text-yellow-400 hover:bg-gray-800">Entrar</Button>
                </Link>
                <Link href="/cadastro">
                  <Button className="bg-gradient-to-r from-yellow-400 to-yellow-500 text-black hover:from-yellow-500 hover:to-yellow-600 font-semibold">Cadastrar</Button>
                </Link>
              </div>
            )}

            {/* Mobile menu */}
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden text-gray-300 hover:text-yellow-400 hover:bg-gray-800"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          </div>
        </div>

        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <div className="md:hidden bg-gray-900 border-t border-yellow-500/20">
            <div className="px-4 py-4 space-y-3">
              <Link
                href="/"
                className="block text-gray-300 hover:text-yellow-400 transition-colors font-medium py-2"
                onClick={() => setMobileMenuOpen(false)}
              >
                Início
              </Link>

              {user ? (
                <>
                  <Link
                    href="/produtos"
                    className="block text-gray-300 hover:text-yellow-400 transition-colors font-medium py-2"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Produtos
                  </Link>
                  <Link
                    href="/pedidos"
                    className="block text-gray-300 hover:text-yellow-400 transition-colors font-medium py-2"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Meus Pedidos
                  </Link>
                  <Link
                    href="/mensagens"
                    className="block text-gray-300 hover:text-yellow-400 transition-colors font-medium py-2"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Mensagens
                  </Link>
                  <Link
                    href="/perfil"
                    className="block text-gray-300 hover:text-yellow-400 transition-colors font-medium py-2"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    Perfil
                  </Link>
                  {userData?.role === 'admin' && (
                    <Link
                      href="/admin"
                      className="block text-yellow-400 hover:text-yellow-300 transition-colors font-medium py-2"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      Admin
                    </Link>
                  )}
                  <Button
                    variant="ghost"
                    onClick={() => {
                      handleSignOut();
                      setMobileMenuOpen(false);
                    }}
                    className="w-full justify-start text-gray-300 hover:text-red-400 hover:bg-gray-800 py-2"
                  >
                    <LogOut className="h-4 w-4 mr-2" />
                    Sair
                  </Button>
                </>
              ) : (
                <div className="space-y-3 pt-2">
                  <Link href="/login" onClick={() => setMobileMenuOpen(false)}>
                    <Button variant="ghost" className="w-full justify-start text-gray-300 hover:text-yellow-400 hover:bg-gray-800">
                      Entrar
                    </Button>
                  </Link>
                  <Link href="/cadastro" onClick={() => setMobileMenuOpen(false)}>
                    <Button className="w-full bg-gradient-to-r from-yellow-400 to-yellow-500 text-black hover:from-yellow-500 hover:to-yellow-600 font-semibold">
                      Cadastrar
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
