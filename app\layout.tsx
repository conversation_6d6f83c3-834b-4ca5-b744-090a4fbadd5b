import type { Metada<PERSON> } from 'next'
import { GeistSans } from 'geist/font/sans'
import { GeistMono } from 'geist/font/mono'
import { Toaster } from 'sonner'
import { AuthProvider } from '../src/components/AuthProvider'
import './globals.css'

export const metadata: Metadata = {
  title: 'doCorpo - E-commerce Premium',
  description: 'Loja online premium com produtos exclusivos e descontos progressivos. Pague com Pix ou depósito bancário.',
  keywords: 'ecommerce, loja online, produtos premium, pix, desconto progressivo',
  authors: [{ name: 'do<PERSON>or<PERSON>' }],
  creator: 'do<PERSON><PERSON><PERSON>',
  publisher: 'doCorpo',
  robots: 'index, follow',
  viewport: 'width=device-width, initial-scale=1',
  themeColor: '#0f172a',
  openGraph: {
    title: 'doCorpo - E-commerce Premium',
    description: 'Loja online premium com produtos exclusivos',
    type: 'website',
    locale: 'pt_BR',
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="pt-BR">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#0f172a" />
        <link rel="icon" href="/favicon.ico" />
      </head>
      <body className={`font-sans ${GeistSans.variable} ${GeistMono.variable}`}>
        <AuthProvider>
          {children}
        </AuthProvider>
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: 'hsl(var(--background))',
              color: 'hsl(var(--foreground))',
              border: '1px solid hsl(var(--border))',
            },
          }}
        />
      </body>
    </html>
  )
}
