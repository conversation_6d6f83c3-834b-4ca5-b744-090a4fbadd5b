﻿import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { getStorage } from 'firebase/storage';

const firebaseConfig = {
  apiKey: "AIzaSyDNB2kaxHwdayO5w2jC4_Bnrvz3_B9Eeqs",
  authDomain: "zmt3utihduf0uxvorbdvk75r9e29pf.firebaseapp.com",
  projectId: "zmt3utihduf0uxvorbdvk75r9e29pf",
  storageBucket: "zmt3utihduf0uxvorbdvk75r9e29pf.firebasestorage.app",
  messagingSenderId: "491725974763",
  appId: "1:491725974763:web:351229f024e5c496bfa2ae",
  measurementId: "G-75MH7DKQ86"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

export default app;
