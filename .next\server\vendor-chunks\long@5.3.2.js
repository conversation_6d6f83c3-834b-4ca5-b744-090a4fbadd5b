/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/long@5.3.2";
exports.ids = ["vendor-chunks/long@5.3.2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/long@5.3.2/node_modules/long/umd/index.js":
/*!**********************************************************************!*\
  !*** ./node_modules/.pnpm/long@5.3.2/node_modules/long/umd/index.js ***!
  \**********************************************************************/
/***/ (function(module, exports) {

eval("var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;// GENERATED FILE. DO NOT EDIT.\n(function (global, factory) {\n  function preferDefault(exports) {\n    return exports.default || exports;\n  }\n  if (true) {\n    !(__WEBPACK_AMD_DEFINE_ARRAY__ = [], __WEBPACK_AMD_DEFINE_RESULT__ = (function () {\n      var exports = {};\n      factory(exports);\n      return preferDefault(exports);\n    }).apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n  } else {}\n})(\n  typeof globalThis !== \"undefined\"\n    ? globalThis\n    : typeof self !== \"undefined\"\n      ? self\n      : this,\n  function (_exports) {\n    \"use strict\";\n\n    Object.defineProperty(_exports, \"__esModule\", {\n      value: true,\n    });\n    _exports.default = void 0;\n    /**\n     * @license\n     * Copyright 2009 The Closure Library Authors\n     * Copyright 2020 Daniel Wirtz / The long.js Authors.\n     *\n     * Licensed under the Apache License, Version 2.0 (the \"License\");\n     * you may not use this file except in compliance with the License.\n     * You may obtain a copy of the License at\n     *\n     *     http://www.apache.org/licenses/LICENSE-2.0\n     *\n     * Unless required by applicable law or agreed to in writing, software\n     * distributed under the License is distributed on an \"AS IS\" BASIS,\n     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n     * See the License for the specific language governing permissions and\n     * limitations under the License.\n     *\n     * SPDX-License-Identifier: Apache-2.0\n     */\n\n    // WebAssembly optimizations to do native i64 multiplication and divide\n    var wasm = null;\n    try {\n      wasm = new WebAssembly.Instance(\n        new WebAssembly.Module(\n          new Uint8Array([\n            // \\0asm\n            0, 97, 115, 109,\n            // version 1\n            1, 0, 0, 0,\n            // section \"type\"\n            1, 13, 2,\n            // 0, () => i32\n            96, 0, 1, 127,\n            // 1, (i32, i32, i32, i32) => i32\n            96, 4, 127, 127, 127, 127, 1, 127,\n            // section \"function\"\n            3, 7, 6,\n            // 0, type 0\n            0,\n            // 1, type 1\n            1,\n            // 2, type 1\n            1,\n            // 3, type 1\n            1,\n            // 4, type 1\n            1,\n            // 5, type 1\n            1,\n            // section \"global\"\n            6, 6, 1,\n            // 0, \"high\", mutable i32\n            127, 1, 65, 0, 11,\n            // section \"export\"\n            7, 50, 6,\n            // 0, \"mul\"\n            3, 109, 117, 108, 0, 1,\n            // 1, \"div_s\"\n            5, 100, 105, 118, 95, 115, 0, 2,\n            // 2, \"div_u\"\n            5, 100, 105, 118, 95, 117, 0, 3,\n            // 3, \"rem_s\"\n            5, 114, 101, 109, 95, 115, 0, 4,\n            // 4, \"rem_u\"\n            5, 114, 101, 109, 95, 117, 0, 5,\n            // 5, \"get_high\"\n            8, 103, 101, 116, 95, 104, 105, 103, 104, 0, 0,\n            // section \"code\"\n            10, 191, 1, 6,\n            // 0, \"get_high\"\n            4, 0, 35, 0, 11,\n            // 1, \"mul\"\n            36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173,\n            32, 3, 173, 66, 32, 134, 132, 126, 34, 4, 66, 32, 135, 167, 36, 0,\n            32, 4, 167, 11,\n            // 2, \"div_s\"\n            36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173,\n            32, 3, 173, 66, 32, 134, 132, 127, 34, 4, 66, 32, 135, 167, 36, 0,\n            32, 4, 167, 11,\n            // 3, \"div_u\"\n            36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173,\n            32, 3, 173, 66, 32, 134, 132, 128, 34, 4, 66, 32, 135, 167, 36, 0,\n            32, 4, 167, 11,\n            // 4, \"rem_s\"\n            36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173,\n            32, 3, 173, 66, 32, 134, 132, 129, 34, 4, 66, 32, 135, 167, 36, 0,\n            32, 4, 167, 11,\n            // 5, \"rem_u\"\n            36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173,\n            32, 3, 173, 66, 32, 134, 132, 130, 34, 4, 66, 32, 135, 167, 36, 0,\n            32, 4, 167, 11,\n          ]),\n        ),\n        {},\n      ).exports;\n    } catch {\n      // no wasm support :(\n    }\n\n    /**\n     * Constructs a 64 bit two's-complement integer, given its low and high 32 bit values as *signed* integers.\n     *  See the from* functions below for more convenient ways of constructing Longs.\n     * @exports Long\n     * @class A Long class for representing a 64 bit two's-complement integer value.\n     * @param {number} low The low (signed) 32 bits of the long\n     * @param {number} high The high (signed) 32 bits of the long\n     * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n     * @constructor\n     */\n    function Long(low, high, unsigned) {\n      /**\n       * The low 32 bits as a signed value.\n       * @type {number}\n       */\n      this.low = low | 0;\n\n      /**\n       * The high 32 bits as a signed value.\n       * @type {number}\n       */\n      this.high = high | 0;\n\n      /**\n       * Whether unsigned or not.\n       * @type {boolean}\n       */\n      this.unsigned = !!unsigned;\n    }\n\n    // The internal representation of a long is the two given signed, 32-bit values.\n    // We use 32-bit pieces because these are the size of integers on which\n    // Javascript performs bit-operations.  For operations like addition and\n    // multiplication, we split each number into 16 bit pieces, which can easily be\n    // multiplied within Javascript's floating-point representation without overflow\n    // or change in sign.\n    //\n    // In the algorithms below, we frequently reduce the negative case to the\n    // positive case by negating the input(s) and then post-processing the result.\n    // Note that we must ALWAYS check specially whether those values are MIN_VALUE\n    // (-2^63) because -MIN_VALUE == MIN_VALUE (since 2^63 cannot be represented as\n    // a positive number, it overflows back into a negative).  Not handling this\n    // case would often result in infinite recursion.\n    //\n    // Common constant values ZERO, ONE, NEG_ONE, etc. are defined below the from*\n    // methods on which they depend.\n\n    /**\n     * An indicator used to reliably determine if an object is a Long or not.\n     * @type {boolean}\n     * @const\n     * @private\n     */\n    Long.prototype.__isLong__;\n    Object.defineProperty(Long.prototype, \"__isLong__\", {\n      value: true,\n    });\n\n    /**\n     * @function\n     * @param {*} obj Object\n     * @returns {boolean}\n     * @inner\n     */\n    function isLong(obj) {\n      return (obj && obj[\"__isLong__\"]) === true;\n    }\n\n    /**\n     * @function\n     * @param {*} value number\n     * @returns {number}\n     * @inner\n     */\n    function ctz32(value) {\n      var c = Math.clz32(value & -value);\n      return value ? 31 - c : c;\n    }\n\n    /**\n     * Tests if the specified object is a Long.\n     * @function\n     * @param {*} obj Object\n     * @returns {boolean}\n     */\n    Long.isLong = isLong;\n\n    /**\n     * A cache of the Long representations of small integer values.\n     * @type {!Object}\n     * @inner\n     */\n    var INT_CACHE = {};\n\n    /**\n     * A cache of the Long representations of small unsigned integer values.\n     * @type {!Object}\n     * @inner\n     */\n    var UINT_CACHE = {};\n\n    /**\n     * @param {number} value\n     * @param {boolean=} unsigned\n     * @returns {!Long}\n     * @inner\n     */\n    function fromInt(value, unsigned) {\n      var obj, cachedObj, cache;\n      if (unsigned) {\n        value >>>= 0;\n        if ((cache = 0 <= value && value < 256)) {\n          cachedObj = UINT_CACHE[value];\n          if (cachedObj) return cachedObj;\n        }\n        obj = fromBits(value, 0, true);\n        if (cache) UINT_CACHE[value] = obj;\n        return obj;\n      } else {\n        value |= 0;\n        if ((cache = -128 <= value && value < 128)) {\n          cachedObj = INT_CACHE[value];\n          if (cachedObj) return cachedObj;\n        }\n        obj = fromBits(value, value < 0 ? -1 : 0, false);\n        if (cache) INT_CACHE[value] = obj;\n        return obj;\n      }\n    }\n\n    /**\n     * Returns a Long representing the given 32 bit integer value.\n     * @function\n     * @param {number} value The 32 bit integer in question\n     * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n     * @returns {!Long} The corresponding Long value\n     */\n    Long.fromInt = fromInt;\n\n    /**\n     * @param {number} value\n     * @param {boolean=} unsigned\n     * @returns {!Long}\n     * @inner\n     */\n    function fromNumber(value, unsigned) {\n      if (isNaN(value)) return unsigned ? UZERO : ZERO;\n      if (unsigned) {\n        if (value < 0) return UZERO;\n        if (value >= TWO_PWR_64_DBL) return MAX_UNSIGNED_VALUE;\n      } else {\n        if (value <= -TWO_PWR_63_DBL) return MIN_VALUE;\n        if (value + 1 >= TWO_PWR_63_DBL) return MAX_VALUE;\n      }\n      if (value < 0) return fromNumber(-value, unsigned).neg();\n      return fromBits(\n        value % TWO_PWR_32_DBL | 0,\n        (value / TWO_PWR_32_DBL) | 0,\n        unsigned,\n      );\n    }\n\n    /**\n     * Returns a Long representing the given value, provided that it is a finite number. Otherwise, zero is returned.\n     * @function\n     * @param {number} value The number in question\n     * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n     * @returns {!Long} The corresponding Long value\n     */\n    Long.fromNumber = fromNumber;\n\n    /**\n     * @param {number} lowBits\n     * @param {number} highBits\n     * @param {boolean=} unsigned\n     * @returns {!Long}\n     * @inner\n     */\n    function fromBits(lowBits, highBits, unsigned) {\n      return new Long(lowBits, highBits, unsigned);\n    }\n\n    /**\n     * Returns a Long representing the 64 bit integer that comes by concatenating the given low and high bits. Each is\n     *  assumed to use 32 bits.\n     * @function\n     * @param {number} lowBits The low 32 bits\n     * @param {number} highBits The high 32 bits\n     * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n     * @returns {!Long} The corresponding Long value\n     */\n    Long.fromBits = fromBits;\n\n    /**\n     * @function\n     * @param {number} base\n     * @param {number} exponent\n     * @returns {number}\n     * @inner\n     */\n    var pow_dbl = Math.pow; // Used 4 times (4*8 to 15+4)\n\n    /**\n     * @param {string} str\n     * @param {(boolean|number)=} unsigned\n     * @param {number=} radix\n     * @returns {!Long}\n     * @inner\n     */\n    function fromString(str, unsigned, radix) {\n      if (str.length === 0) throw Error(\"empty string\");\n      if (typeof unsigned === \"number\") {\n        // For goog.math.long compatibility\n        radix = unsigned;\n        unsigned = false;\n      } else {\n        unsigned = !!unsigned;\n      }\n      if (\n        str === \"NaN\" ||\n        str === \"Infinity\" ||\n        str === \"+Infinity\" ||\n        str === \"-Infinity\"\n      )\n        return unsigned ? UZERO : ZERO;\n      radix = radix || 10;\n      if (radix < 2 || 36 < radix) throw RangeError(\"radix\");\n      var p;\n      if ((p = str.indexOf(\"-\")) > 0) throw Error(\"interior hyphen\");\n      else if (p === 0) {\n        return fromString(str.substring(1), unsigned, radix).neg();\n      }\n\n      // Do several (8) digits each time through the loop, so as to\n      // minimize the calls to the very expensive emulated div.\n      var radixToPower = fromNumber(pow_dbl(radix, 8));\n      var result = ZERO;\n      for (var i = 0; i < str.length; i += 8) {\n        var size = Math.min(8, str.length - i),\n          value = parseInt(str.substring(i, i + size), radix);\n        if (size < 8) {\n          var power = fromNumber(pow_dbl(radix, size));\n          result = result.mul(power).add(fromNumber(value));\n        } else {\n          result = result.mul(radixToPower);\n          result = result.add(fromNumber(value));\n        }\n      }\n      result.unsigned = unsigned;\n      return result;\n    }\n\n    /**\n     * Returns a Long representation of the given string, written using the specified radix.\n     * @function\n     * @param {string} str The textual representation of the Long\n     * @param {(boolean|number)=} unsigned Whether unsigned or not, defaults to signed\n     * @param {number=} radix The radix in which the text is written (2-36), defaults to 10\n     * @returns {!Long} The corresponding Long value\n     */\n    Long.fromString = fromString;\n\n    /**\n     * @function\n     * @param {!Long|number|string|!{low: number, high: number, unsigned: boolean}} val\n     * @param {boolean=} unsigned\n     * @returns {!Long}\n     * @inner\n     */\n    function fromValue(val, unsigned) {\n      if (typeof val === \"number\") return fromNumber(val, unsigned);\n      if (typeof val === \"string\") return fromString(val, unsigned);\n      // Throws for non-objects, converts non-instanceof Long:\n      return fromBits(\n        val.low,\n        val.high,\n        typeof unsigned === \"boolean\" ? unsigned : val.unsigned,\n      );\n    }\n\n    /**\n     * Converts the specified value to a Long using the appropriate from* function for its type.\n     * @function\n     * @param {!Long|number|bigint|string|!{low: number, high: number, unsigned: boolean}} val Value\n     * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n     * @returns {!Long}\n     */\n    Long.fromValue = fromValue;\n\n    // NOTE: the compiler should inline these constant values below and then remove these variables, so there should be\n    // no runtime penalty for these.\n\n    /**\n     * @type {number}\n     * @const\n     * @inner\n     */\n    var TWO_PWR_16_DBL = 1 << 16;\n\n    /**\n     * @type {number}\n     * @const\n     * @inner\n     */\n    var TWO_PWR_24_DBL = 1 << 24;\n\n    /**\n     * @type {number}\n     * @const\n     * @inner\n     */\n    var TWO_PWR_32_DBL = TWO_PWR_16_DBL * TWO_PWR_16_DBL;\n\n    /**\n     * @type {number}\n     * @const\n     * @inner\n     */\n    var TWO_PWR_64_DBL = TWO_PWR_32_DBL * TWO_PWR_32_DBL;\n\n    /**\n     * @type {number}\n     * @const\n     * @inner\n     */\n    var TWO_PWR_63_DBL = TWO_PWR_64_DBL / 2;\n\n    /**\n     * @type {!Long}\n     * @const\n     * @inner\n     */\n    var TWO_PWR_24 = fromInt(TWO_PWR_24_DBL);\n\n    /**\n     * @type {!Long}\n     * @inner\n     */\n    var ZERO = fromInt(0);\n\n    /**\n     * Signed zero.\n     * @type {!Long}\n     */\n    Long.ZERO = ZERO;\n\n    /**\n     * @type {!Long}\n     * @inner\n     */\n    var UZERO = fromInt(0, true);\n\n    /**\n     * Unsigned zero.\n     * @type {!Long}\n     */\n    Long.UZERO = UZERO;\n\n    /**\n     * @type {!Long}\n     * @inner\n     */\n    var ONE = fromInt(1);\n\n    /**\n     * Signed one.\n     * @type {!Long}\n     */\n    Long.ONE = ONE;\n\n    /**\n     * @type {!Long}\n     * @inner\n     */\n    var UONE = fromInt(1, true);\n\n    /**\n     * Unsigned one.\n     * @type {!Long}\n     */\n    Long.UONE = UONE;\n\n    /**\n     * @type {!Long}\n     * @inner\n     */\n    var NEG_ONE = fromInt(-1);\n\n    /**\n     * Signed negative one.\n     * @type {!Long}\n     */\n    Long.NEG_ONE = NEG_ONE;\n\n    /**\n     * @type {!Long}\n     * @inner\n     */\n    var MAX_VALUE = fromBits(0xffffffff | 0, 0x7fffffff | 0, false);\n\n    /**\n     * Maximum signed value.\n     * @type {!Long}\n     */\n    Long.MAX_VALUE = MAX_VALUE;\n\n    /**\n     * @type {!Long}\n     * @inner\n     */\n    var MAX_UNSIGNED_VALUE = fromBits(0xffffffff | 0, 0xffffffff | 0, true);\n\n    /**\n     * Maximum unsigned value.\n     * @type {!Long}\n     */\n    Long.MAX_UNSIGNED_VALUE = MAX_UNSIGNED_VALUE;\n\n    /**\n     * @type {!Long}\n     * @inner\n     */\n    var MIN_VALUE = fromBits(0, 0x80000000 | 0, false);\n\n    /**\n     * Minimum signed value.\n     * @type {!Long}\n     */\n    Long.MIN_VALUE = MIN_VALUE;\n\n    /**\n     * @alias Long.prototype\n     * @inner\n     */\n    var LongPrototype = Long.prototype;\n\n    /**\n     * Converts the Long to a 32 bit integer, assuming it is a 32 bit integer.\n     * @this {!Long}\n     * @returns {number}\n     */\n    LongPrototype.toInt = function toInt() {\n      return this.unsigned ? this.low >>> 0 : this.low;\n    };\n\n    /**\n     * Converts the Long to a the nearest floating-point representation of this value (double, 53 bit mantissa).\n     * @this {!Long}\n     * @returns {number}\n     */\n    LongPrototype.toNumber = function toNumber() {\n      if (this.unsigned)\n        return (this.high >>> 0) * TWO_PWR_32_DBL + (this.low >>> 0);\n      return this.high * TWO_PWR_32_DBL + (this.low >>> 0);\n    };\n\n    /**\n     * Converts the Long to a string written in the specified radix.\n     * @this {!Long}\n     * @param {number=} radix Radix (2-36), defaults to 10\n     * @returns {string}\n     * @override\n     * @throws {RangeError} If `radix` is out of range\n     */\n    LongPrototype.toString = function toString(radix) {\n      radix = radix || 10;\n      if (radix < 2 || 36 < radix) throw RangeError(\"radix\");\n      if (this.isZero()) return \"0\";\n      if (this.isNegative()) {\n        // Unsigned Longs are never negative\n        if (this.eq(MIN_VALUE)) {\n          // We need to change the Long value before it can be negated, so we remove\n          // the bottom-most digit in this base and then recurse to do the rest.\n          var radixLong = fromNumber(radix),\n            div = this.div(radixLong),\n            rem1 = div.mul(radixLong).sub(this);\n          return div.toString(radix) + rem1.toInt().toString(radix);\n        } else return \"-\" + this.neg().toString(radix);\n      }\n\n      // Do several (6) digits each time through the loop, so as to\n      // minimize the calls to the very expensive emulated div.\n      var radixToPower = fromNumber(pow_dbl(radix, 6), this.unsigned),\n        rem = this;\n      var result = \"\";\n      while (true) {\n        var remDiv = rem.div(radixToPower),\n          intval = rem.sub(remDiv.mul(radixToPower)).toInt() >>> 0,\n          digits = intval.toString(radix);\n        rem = remDiv;\n        if (rem.isZero()) return digits + result;\n        else {\n          while (digits.length < 6) digits = \"0\" + digits;\n          result = \"\" + digits + result;\n        }\n      }\n    };\n\n    /**\n     * Gets the high 32 bits as a signed integer.\n     * @this {!Long}\n     * @returns {number} Signed high bits\n     */\n    LongPrototype.getHighBits = function getHighBits() {\n      return this.high;\n    };\n\n    /**\n     * Gets the high 32 bits as an unsigned integer.\n     * @this {!Long}\n     * @returns {number} Unsigned high bits\n     */\n    LongPrototype.getHighBitsUnsigned = function getHighBitsUnsigned() {\n      return this.high >>> 0;\n    };\n\n    /**\n     * Gets the low 32 bits as a signed integer.\n     * @this {!Long}\n     * @returns {number} Signed low bits\n     */\n    LongPrototype.getLowBits = function getLowBits() {\n      return this.low;\n    };\n\n    /**\n     * Gets the low 32 bits as an unsigned integer.\n     * @this {!Long}\n     * @returns {number} Unsigned low bits\n     */\n    LongPrototype.getLowBitsUnsigned = function getLowBitsUnsigned() {\n      return this.low >>> 0;\n    };\n\n    /**\n     * Gets the number of bits needed to represent the absolute value of this Long.\n     * @this {!Long}\n     * @returns {number}\n     */\n    LongPrototype.getNumBitsAbs = function getNumBitsAbs() {\n      if (this.isNegative())\n        // Unsigned Longs are never negative\n        return this.eq(MIN_VALUE) ? 64 : this.neg().getNumBitsAbs();\n      var val = this.high != 0 ? this.high : this.low;\n      for (var bit = 31; bit > 0; bit--) if ((val & (1 << bit)) != 0) break;\n      return this.high != 0 ? bit + 33 : bit + 1;\n    };\n\n    /**\n     * Tests if this Long can be safely represented as a JavaScript number.\n     * @this {!Long}\n     * @returns {boolean}\n     */\n    LongPrototype.isSafeInteger = function isSafeInteger() {\n      // 2^53-1 is the maximum safe value\n      var top11Bits = this.high >> 21;\n      // [0, 2^53-1]\n      if (!top11Bits) return true;\n      // > 2^53-1\n      if (this.unsigned) return false;\n      // [-2^53, -1] except -2^53\n      return top11Bits === -1 && !(this.low === 0 && this.high === -0x200000);\n    };\n\n    /**\n     * Tests if this Long's value equals zero.\n     * @this {!Long}\n     * @returns {boolean}\n     */\n    LongPrototype.isZero = function isZero() {\n      return this.high === 0 && this.low === 0;\n    };\n\n    /**\n     * Tests if this Long's value equals zero. This is an alias of {@link Long#isZero}.\n     * @returns {boolean}\n     */\n    LongPrototype.eqz = LongPrototype.isZero;\n\n    /**\n     * Tests if this Long's value is negative.\n     * @this {!Long}\n     * @returns {boolean}\n     */\n    LongPrototype.isNegative = function isNegative() {\n      return !this.unsigned && this.high < 0;\n    };\n\n    /**\n     * Tests if this Long's value is positive or zero.\n     * @this {!Long}\n     * @returns {boolean}\n     */\n    LongPrototype.isPositive = function isPositive() {\n      return this.unsigned || this.high >= 0;\n    };\n\n    /**\n     * Tests if this Long's value is odd.\n     * @this {!Long}\n     * @returns {boolean}\n     */\n    LongPrototype.isOdd = function isOdd() {\n      return (this.low & 1) === 1;\n    };\n\n    /**\n     * Tests if this Long's value is even.\n     * @this {!Long}\n     * @returns {boolean}\n     */\n    LongPrototype.isEven = function isEven() {\n      return (this.low & 1) === 0;\n    };\n\n    /**\n     * Tests if this Long's value equals the specified's.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.equals = function equals(other) {\n      if (!isLong(other)) other = fromValue(other);\n      if (\n        this.unsigned !== other.unsigned &&\n        this.high >>> 31 === 1 &&\n        other.high >>> 31 === 1\n      )\n        return false;\n      return this.high === other.high && this.low === other.low;\n    };\n\n    /**\n     * Tests if this Long's value equals the specified's. This is an alias of {@link Long#equals}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.eq = LongPrototype.equals;\n\n    /**\n     * Tests if this Long's value differs from the specified's.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.notEquals = function notEquals(other) {\n      return !this.eq(/* validates */ other);\n    };\n\n    /**\n     * Tests if this Long's value differs from the specified's. This is an alias of {@link Long#notEquals}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.neq = LongPrototype.notEquals;\n\n    /**\n     * Tests if this Long's value differs from the specified's. This is an alias of {@link Long#notEquals}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.ne = LongPrototype.notEquals;\n\n    /**\n     * Tests if this Long's value is less than the specified's.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.lessThan = function lessThan(other) {\n      return this.comp(/* validates */ other) < 0;\n    };\n\n    /**\n     * Tests if this Long's value is less than the specified's. This is an alias of {@link Long#lessThan}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.lt = LongPrototype.lessThan;\n\n    /**\n     * Tests if this Long's value is less than or equal the specified's.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.lessThanOrEqual = function lessThanOrEqual(other) {\n      return this.comp(/* validates */ other) <= 0;\n    };\n\n    /**\n     * Tests if this Long's value is less than or equal the specified's. This is an alias of {@link Long#lessThanOrEqual}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.lte = LongPrototype.lessThanOrEqual;\n\n    /**\n     * Tests if this Long's value is less than or equal the specified's. This is an alias of {@link Long#lessThanOrEqual}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.le = LongPrototype.lessThanOrEqual;\n\n    /**\n     * Tests if this Long's value is greater than the specified's.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.greaterThan = function greaterThan(other) {\n      return this.comp(/* validates */ other) > 0;\n    };\n\n    /**\n     * Tests if this Long's value is greater than the specified's. This is an alias of {@link Long#greaterThan}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.gt = LongPrototype.greaterThan;\n\n    /**\n     * Tests if this Long's value is greater than or equal the specified's.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.greaterThanOrEqual = function greaterThanOrEqual(other) {\n      return this.comp(/* validates */ other) >= 0;\n    };\n\n    /**\n     * Tests if this Long's value is greater than or equal the specified's. This is an alias of {@link Long#greaterThanOrEqual}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.gte = LongPrototype.greaterThanOrEqual;\n\n    /**\n     * Tests if this Long's value is greater than or equal the specified's. This is an alias of {@link Long#greaterThanOrEqual}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {boolean}\n     */\n    LongPrototype.ge = LongPrototype.greaterThanOrEqual;\n\n    /**\n     * Compares this Long's value with the specified's.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {number} 0 if they are the same, 1 if the this is greater and -1\n     *  if the given one is greater\n     */\n    LongPrototype.compare = function compare(other) {\n      if (!isLong(other)) other = fromValue(other);\n      if (this.eq(other)) return 0;\n      var thisNeg = this.isNegative(),\n        otherNeg = other.isNegative();\n      if (thisNeg && !otherNeg) return -1;\n      if (!thisNeg && otherNeg) return 1;\n      // At this point the sign bits are the same\n      if (!this.unsigned) return this.sub(other).isNegative() ? -1 : 1;\n      // Both are positive if at least one is unsigned\n      return other.high >>> 0 > this.high >>> 0 ||\n        (other.high === this.high && other.low >>> 0 > this.low >>> 0)\n        ? -1\n        : 1;\n    };\n\n    /**\n     * Compares this Long's value with the specified's. This is an alias of {@link Long#compare}.\n     * @function\n     * @param {!Long|number|bigint|string} other Other value\n     * @returns {number} 0 if they are the same, 1 if the this is greater and -1\n     *  if the given one is greater\n     */\n    LongPrototype.comp = LongPrototype.compare;\n\n    /**\n     * Negates this Long's value.\n     * @this {!Long}\n     * @returns {!Long} Negated Long\n     */\n    LongPrototype.negate = function negate() {\n      if (!this.unsigned && this.eq(MIN_VALUE)) return MIN_VALUE;\n      return this.not().add(ONE);\n    };\n\n    /**\n     * Negates this Long's value. This is an alias of {@link Long#negate}.\n     * @function\n     * @returns {!Long} Negated Long\n     */\n    LongPrototype.neg = LongPrototype.negate;\n\n    /**\n     * Returns the sum of this and the specified Long.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} addend Addend\n     * @returns {!Long} Sum\n     */\n    LongPrototype.add = function add(addend) {\n      if (!isLong(addend)) addend = fromValue(addend);\n\n      // Divide each number into 4 chunks of 16 bits, and then sum the chunks.\n\n      var a48 = this.high >>> 16;\n      var a32 = this.high & 0xffff;\n      var a16 = this.low >>> 16;\n      var a00 = this.low & 0xffff;\n      var b48 = addend.high >>> 16;\n      var b32 = addend.high & 0xffff;\n      var b16 = addend.low >>> 16;\n      var b00 = addend.low & 0xffff;\n      var c48 = 0,\n        c32 = 0,\n        c16 = 0,\n        c00 = 0;\n      c00 += a00 + b00;\n      c16 += c00 >>> 16;\n      c00 &= 0xffff;\n      c16 += a16 + b16;\n      c32 += c16 >>> 16;\n      c16 &= 0xffff;\n      c32 += a32 + b32;\n      c48 += c32 >>> 16;\n      c32 &= 0xffff;\n      c48 += a48 + b48;\n      c48 &= 0xffff;\n      return fromBits((c16 << 16) | c00, (c48 << 16) | c32, this.unsigned);\n    };\n\n    /**\n     * Returns the difference of this and the specified Long.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} subtrahend Subtrahend\n     * @returns {!Long} Difference\n     */\n    LongPrototype.subtract = function subtract(subtrahend) {\n      if (!isLong(subtrahend)) subtrahend = fromValue(subtrahend);\n      return this.add(subtrahend.neg());\n    };\n\n    /**\n     * Returns the difference of this and the specified Long. This is an alias of {@link Long#subtract}.\n     * @function\n     * @param {!Long|number|bigint|string} subtrahend Subtrahend\n     * @returns {!Long} Difference\n     */\n    LongPrototype.sub = LongPrototype.subtract;\n\n    /**\n     * Returns the product of this and the specified Long.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} multiplier Multiplier\n     * @returns {!Long} Product\n     */\n    LongPrototype.multiply = function multiply(multiplier) {\n      if (this.isZero()) return this;\n      if (!isLong(multiplier)) multiplier = fromValue(multiplier);\n\n      // use wasm support if present\n      if (wasm) {\n        var low = wasm[\"mul\"](\n          this.low,\n          this.high,\n          multiplier.low,\n          multiplier.high,\n        );\n        return fromBits(low, wasm[\"get_high\"](), this.unsigned);\n      }\n      if (multiplier.isZero()) return this.unsigned ? UZERO : ZERO;\n      if (this.eq(MIN_VALUE)) return multiplier.isOdd() ? MIN_VALUE : ZERO;\n      if (multiplier.eq(MIN_VALUE)) return this.isOdd() ? MIN_VALUE : ZERO;\n      if (this.isNegative()) {\n        if (multiplier.isNegative()) return this.neg().mul(multiplier.neg());\n        else return this.neg().mul(multiplier).neg();\n      } else if (multiplier.isNegative())\n        return this.mul(multiplier.neg()).neg();\n\n      // If both longs are small, use float multiplication\n      if (this.lt(TWO_PWR_24) && multiplier.lt(TWO_PWR_24))\n        return fromNumber(\n          this.toNumber() * multiplier.toNumber(),\n          this.unsigned,\n        );\n\n      // Divide each long into 4 chunks of 16 bits, and then add up 4x4 products.\n      // We can skip products that would overflow.\n\n      var a48 = this.high >>> 16;\n      var a32 = this.high & 0xffff;\n      var a16 = this.low >>> 16;\n      var a00 = this.low & 0xffff;\n      var b48 = multiplier.high >>> 16;\n      var b32 = multiplier.high & 0xffff;\n      var b16 = multiplier.low >>> 16;\n      var b00 = multiplier.low & 0xffff;\n      var c48 = 0,\n        c32 = 0,\n        c16 = 0,\n        c00 = 0;\n      c00 += a00 * b00;\n      c16 += c00 >>> 16;\n      c00 &= 0xffff;\n      c16 += a16 * b00;\n      c32 += c16 >>> 16;\n      c16 &= 0xffff;\n      c16 += a00 * b16;\n      c32 += c16 >>> 16;\n      c16 &= 0xffff;\n      c32 += a32 * b00;\n      c48 += c32 >>> 16;\n      c32 &= 0xffff;\n      c32 += a16 * b16;\n      c48 += c32 >>> 16;\n      c32 &= 0xffff;\n      c32 += a00 * b32;\n      c48 += c32 >>> 16;\n      c32 &= 0xffff;\n      c48 += a48 * b00 + a32 * b16 + a16 * b32 + a00 * b48;\n      c48 &= 0xffff;\n      return fromBits((c16 << 16) | c00, (c48 << 16) | c32, this.unsigned);\n    };\n\n    /**\n     * Returns the product of this and the specified Long. This is an alias of {@link Long#multiply}.\n     * @function\n     * @param {!Long|number|bigint|string} multiplier Multiplier\n     * @returns {!Long} Product\n     */\n    LongPrototype.mul = LongPrototype.multiply;\n\n    /**\n     * Returns this Long divided by the specified. The result is signed if this Long is signed or\n     *  unsigned if this Long is unsigned.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} divisor Divisor\n     * @returns {!Long} Quotient\n     */\n    LongPrototype.divide = function divide(divisor) {\n      if (!isLong(divisor)) divisor = fromValue(divisor);\n      if (divisor.isZero()) throw Error(\"division by zero\");\n\n      // use wasm support if present\n      if (wasm) {\n        // guard against signed division overflow: the largest\n        // negative number / -1 would be 1 larger than the largest\n        // positive number, due to two's complement.\n        if (\n          !this.unsigned &&\n          this.high === -0x80000000 &&\n          divisor.low === -1 &&\n          divisor.high === -1\n        ) {\n          // be consistent with non-wasm code path\n          return this;\n        }\n        var low = (this.unsigned ? wasm[\"div_u\"] : wasm[\"div_s\"])(\n          this.low,\n          this.high,\n          divisor.low,\n          divisor.high,\n        );\n        return fromBits(low, wasm[\"get_high\"](), this.unsigned);\n      }\n      if (this.isZero()) return this.unsigned ? UZERO : ZERO;\n      var approx, rem, res;\n      if (!this.unsigned) {\n        // This section is only relevant for signed longs and is derived from the\n        // closure library as a whole.\n        if (this.eq(MIN_VALUE)) {\n          if (divisor.eq(ONE) || divisor.eq(NEG_ONE))\n            return MIN_VALUE; // recall that -MIN_VALUE == MIN_VALUE\n          else if (divisor.eq(MIN_VALUE)) return ONE;\n          else {\n            // At this point, we have |other| >= 2, so |this/other| < |MIN_VALUE|.\n            var halfThis = this.shr(1);\n            approx = halfThis.div(divisor).shl(1);\n            if (approx.eq(ZERO)) {\n              return divisor.isNegative() ? ONE : NEG_ONE;\n            } else {\n              rem = this.sub(divisor.mul(approx));\n              res = approx.add(rem.div(divisor));\n              return res;\n            }\n          }\n        } else if (divisor.eq(MIN_VALUE)) return this.unsigned ? UZERO : ZERO;\n        if (this.isNegative()) {\n          if (divisor.isNegative()) return this.neg().div(divisor.neg());\n          return this.neg().div(divisor).neg();\n        } else if (divisor.isNegative()) return this.div(divisor.neg()).neg();\n        res = ZERO;\n      } else {\n        // The algorithm below has not been made for unsigned longs. It's therefore\n        // required to take special care of the MSB prior to running it.\n        if (!divisor.unsigned) divisor = divisor.toUnsigned();\n        if (divisor.gt(this)) return UZERO;\n        if (divisor.gt(this.shru(1)))\n          // 15 >>> 1 = 7 ; with divisor = 8 ; true\n          return UONE;\n        res = UZERO;\n      }\n\n      // Repeat the following until the remainder is less than other:  find a\n      // floating-point that approximates remainder / other *from below*, add this\n      // into the result, and subtract it from the remainder.  It is critical that\n      // the approximate value is less than or equal to the real value so that the\n      // remainder never becomes negative.\n      rem = this;\n      while (rem.gte(divisor)) {\n        // Approximate the result of division. This may be a little greater or\n        // smaller than the actual value.\n        approx = Math.max(1, Math.floor(rem.toNumber() / divisor.toNumber()));\n\n        // We will tweak the approximate result by changing it in the 48-th digit or\n        // the smallest non-fractional digit, whichever is larger.\n        var log2 = Math.ceil(Math.log(approx) / Math.LN2),\n          delta = log2 <= 48 ? 1 : pow_dbl(2, log2 - 48),\n          // Decrease the approximation until it is smaller than the remainder.  Note\n          // that if it is too large, the product overflows and is negative.\n          approxRes = fromNumber(approx),\n          approxRem = approxRes.mul(divisor);\n        while (approxRem.isNegative() || approxRem.gt(rem)) {\n          approx -= delta;\n          approxRes = fromNumber(approx, this.unsigned);\n          approxRem = approxRes.mul(divisor);\n        }\n\n        // We know the answer can't be zero... and actually, zero would cause\n        // infinite recursion since we would make no progress.\n        if (approxRes.isZero()) approxRes = ONE;\n        res = res.add(approxRes);\n        rem = rem.sub(approxRem);\n      }\n      return res;\n    };\n\n    /**\n     * Returns this Long divided by the specified. This is an alias of {@link Long#divide}.\n     * @function\n     * @param {!Long|number|bigint|string} divisor Divisor\n     * @returns {!Long} Quotient\n     */\n    LongPrototype.div = LongPrototype.divide;\n\n    /**\n     * Returns this Long modulo the specified.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} divisor Divisor\n     * @returns {!Long} Remainder\n     */\n    LongPrototype.modulo = function modulo(divisor) {\n      if (!isLong(divisor)) divisor = fromValue(divisor);\n\n      // use wasm support if present\n      if (wasm) {\n        var low = (this.unsigned ? wasm[\"rem_u\"] : wasm[\"rem_s\"])(\n          this.low,\n          this.high,\n          divisor.low,\n          divisor.high,\n        );\n        return fromBits(low, wasm[\"get_high\"](), this.unsigned);\n      }\n      return this.sub(this.div(divisor).mul(divisor));\n    };\n\n    /**\n     * Returns this Long modulo the specified. This is an alias of {@link Long#modulo}.\n     * @function\n     * @param {!Long|number|bigint|string} divisor Divisor\n     * @returns {!Long} Remainder\n     */\n    LongPrototype.mod = LongPrototype.modulo;\n\n    /**\n     * Returns this Long modulo the specified. This is an alias of {@link Long#modulo}.\n     * @function\n     * @param {!Long|number|bigint|string} divisor Divisor\n     * @returns {!Long} Remainder\n     */\n    LongPrototype.rem = LongPrototype.modulo;\n\n    /**\n     * Returns the bitwise NOT of this Long.\n     * @this {!Long}\n     * @returns {!Long}\n     */\n    LongPrototype.not = function not() {\n      return fromBits(~this.low, ~this.high, this.unsigned);\n    };\n\n    /**\n     * Returns count leading zeros of this Long.\n     * @this {!Long}\n     * @returns {!number}\n     */\n    LongPrototype.countLeadingZeros = function countLeadingZeros() {\n      return this.high ? Math.clz32(this.high) : Math.clz32(this.low) + 32;\n    };\n\n    /**\n     * Returns count leading zeros. This is an alias of {@link Long#countLeadingZeros}.\n     * @function\n     * @param {!Long}\n     * @returns {!number}\n     */\n    LongPrototype.clz = LongPrototype.countLeadingZeros;\n\n    /**\n     * Returns count trailing zeros of this Long.\n     * @this {!Long}\n     * @returns {!number}\n     */\n    LongPrototype.countTrailingZeros = function countTrailingZeros() {\n      return this.low ? ctz32(this.low) : ctz32(this.high) + 32;\n    };\n\n    /**\n     * Returns count trailing zeros. This is an alias of {@link Long#countTrailingZeros}.\n     * @function\n     * @param {!Long}\n     * @returns {!number}\n     */\n    LongPrototype.ctz = LongPrototype.countTrailingZeros;\n\n    /**\n     * Returns the bitwise AND of this Long and the specified.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other Long\n     * @returns {!Long}\n     */\n    LongPrototype.and = function and(other) {\n      if (!isLong(other)) other = fromValue(other);\n      return fromBits(\n        this.low & other.low,\n        this.high & other.high,\n        this.unsigned,\n      );\n    };\n\n    /**\n     * Returns the bitwise OR of this Long and the specified.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other Long\n     * @returns {!Long}\n     */\n    LongPrototype.or = function or(other) {\n      if (!isLong(other)) other = fromValue(other);\n      return fromBits(\n        this.low | other.low,\n        this.high | other.high,\n        this.unsigned,\n      );\n    };\n\n    /**\n     * Returns the bitwise XOR of this Long and the given one.\n     * @this {!Long}\n     * @param {!Long|number|bigint|string} other Other Long\n     * @returns {!Long}\n     */\n    LongPrototype.xor = function xor(other) {\n      if (!isLong(other)) other = fromValue(other);\n      return fromBits(\n        this.low ^ other.low,\n        this.high ^ other.high,\n        this.unsigned,\n      );\n    };\n\n    /**\n     * Returns this Long with bits shifted to the left by the given amount.\n     * @this {!Long}\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Shifted Long\n     */\n    LongPrototype.shiftLeft = function shiftLeft(numBits) {\n      if (isLong(numBits)) numBits = numBits.toInt();\n      if ((numBits &= 63) === 0) return this;\n      else if (numBits < 32)\n        return fromBits(\n          this.low << numBits,\n          (this.high << numBits) | (this.low >>> (32 - numBits)),\n          this.unsigned,\n        );\n      else return fromBits(0, this.low << (numBits - 32), this.unsigned);\n    };\n\n    /**\n     * Returns this Long with bits shifted to the left by the given amount. This is an alias of {@link Long#shiftLeft}.\n     * @function\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Shifted Long\n     */\n    LongPrototype.shl = LongPrototype.shiftLeft;\n\n    /**\n     * Returns this Long with bits arithmetically shifted to the right by the given amount.\n     * @this {!Long}\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Shifted Long\n     */\n    LongPrototype.shiftRight = function shiftRight(numBits) {\n      if (isLong(numBits)) numBits = numBits.toInt();\n      if ((numBits &= 63) === 0) return this;\n      else if (numBits < 32)\n        return fromBits(\n          (this.low >>> numBits) | (this.high << (32 - numBits)),\n          this.high >> numBits,\n          this.unsigned,\n        );\n      else\n        return fromBits(\n          this.high >> (numBits - 32),\n          this.high >= 0 ? 0 : -1,\n          this.unsigned,\n        );\n    };\n\n    /**\n     * Returns this Long with bits arithmetically shifted to the right by the given amount. This is an alias of {@link Long#shiftRight}.\n     * @function\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Shifted Long\n     */\n    LongPrototype.shr = LongPrototype.shiftRight;\n\n    /**\n     * Returns this Long with bits logically shifted to the right by the given amount.\n     * @this {!Long}\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Shifted Long\n     */\n    LongPrototype.shiftRightUnsigned = function shiftRightUnsigned(numBits) {\n      if (isLong(numBits)) numBits = numBits.toInt();\n      if ((numBits &= 63) === 0) return this;\n      if (numBits < 32)\n        return fromBits(\n          (this.low >>> numBits) | (this.high << (32 - numBits)),\n          this.high >>> numBits,\n          this.unsigned,\n        );\n      if (numBits === 32) return fromBits(this.high, 0, this.unsigned);\n      return fromBits(this.high >>> (numBits - 32), 0, this.unsigned);\n    };\n\n    /**\n     * Returns this Long with bits logically shifted to the right by the given amount. This is an alias of {@link Long#shiftRightUnsigned}.\n     * @function\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Shifted Long\n     */\n    LongPrototype.shru = LongPrototype.shiftRightUnsigned;\n\n    /**\n     * Returns this Long with bits logically shifted to the right by the given amount. This is an alias of {@link Long#shiftRightUnsigned}.\n     * @function\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Shifted Long\n     */\n    LongPrototype.shr_u = LongPrototype.shiftRightUnsigned;\n\n    /**\n     * Returns this Long with bits rotated to the left by the given amount.\n     * @this {!Long}\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Rotated Long\n     */\n    LongPrototype.rotateLeft = function rotateLeft(numBits) {\n      var b;\n      if (isLong(numBits)) numBits = numBits.toInt();\n      if ((numBits &= 63) === 0) return this;\n      if (numBits === 32) return fromBits(this.high, this.low, this.unsigned);\n      if (numBits < 32) {\n        b = 32 - numBits;\n        return fromBits(\n          (this.low << numBits) | (this.high >>> b),\n          (this.high << numBits) | (this.low >>> b),\n          this.unsigned,\n        );\n      }\n      numBits -= 32;\n      b = 32 - numBits;\n      return fromBits(\n        (this.high << numBits) | (this.low >>> b),\n        (this.low << numBits) | (this.high >>> b),\n        this.unsigned,\n      );\n    };\n    /**\n     * Returns this Long with bits rotated to the left by the given amount. This is an alias of {@link Long#rotateLeft}.\n     * @function\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Rotated Long\n     */\n    LongPrototype.rotl = LongPrototype.rotateLeft;\n\n    /**\n     * Returns this Long with bits rotated to the right by the given amount.\n     * @this {!Long}\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Rotated Long\n     */\n    LongPrototype.rotateRight = function rotateRight(numBits) {\n      var b;\n      if (isLong(numBits)) numBits = numBits.toInt();\n      if ((numBits &= 63) === 0) return this;\n      if (numBits === 32) return fromBits(this.high, this.low, this.unsigned);\n      if (numBits < 32) {\n        b = 32 - numBits;\n        return fromBits(\n          (this.high << b) | (this.low >>> numBits),\n          (this.low << b) | (this.high >>> numBits),\n          this.unsigned,\n        );\n      }\n      numBits -= 32;\n      b = 32 - numBits;\n      return fromBits(\n        (this.low << b) | (this.high >>> numBits),\n        (this.high << b) | (this.low >>> numBits),\n        this.unsigned,\n      );\n    };\n    /**\n     * Returns this Long with bits rotated to the right by the given amount. This is an alias of {@link Long#rotateRight}.\n     * @function\n     * @param {number|!Long} numBits Number of bits\n     * @returns {!Long} Rotated Long\n     */\n    LongPrototype.rotr = LongPrototype.rotateRight;\n\n    /**\n     * Converts this Long to signed.\n     * @this {!Long}\n     * @returns {!Long} Signed long\n     */\n    LongPrototype.toSigned = function toSigned() {\n      if (!this.unsigned) return this;\n      return fromBits(this.low, this.high, false);\n    };\n\n    /**\n     * Converts this Long to unsigned.\n     * @this {!Long}\n     * @returns {!Long} Unsigned long\n     */\n    LongPrototype.toUnsigned = function toUnsigned() {\n      if (this.unsigned) return this;\n      return fromBits(this.low, this.high, true);\n    };\n\n    /**\n     * Converts this Long to its byte representation.\n     * @param {boolean=} le Whether little or big endian, defaults to big endian\n     * @this {!Long}\n     * @returns {!Array.<number>} Byte representation\n     */\n    LongPrototype.toBytes = function toBytes(le) {\n      return le ? this.toBytesLE() : this.toBytesBE();\n    };\n\n    /**\n     * Converts this Long to its little endian byte representation.\n     * @this {!Long}\n     * @returns {!Array.<number>} Little endian byte representation\n     */\n    LongPrototype.toBytesLE = function toBytesLE() {\n      var hi = this.high,\n        lo = this.low;\n      return [\n        lo & 0xff,\n        (lo >>> 8) & 0xff,\n        (lo >>> 16) & 0xff,\n        lo >>> 24,\n        hi & 0xff,\n        (hi >>> 8) & 0xff,\n        (hi >>> 16) & 0xff,\n        hi >>> 24,\n      ];\n    };\n\n    /**\n     * Converts this Long to its big endian byte representation.\n     * @this {!Long}\n     * @returns {!Array.<number>} Big endian byte representation\n     */\n    LongPrototype.toBytesBE = function toBytesBE() {\n      var hi = this.high,\n        lo = this.low;\n      return [\n        hi >>> 24,\n        (hi >>> 16) & 0xff,\n        (hi >>> 8) & 0xff,\n        hi & 0xff,\n        lo >>> 24,\n        (lo >>> 16) & 0xff,\n        (lo >>> 8) & 0xff,\n        lo & 0xff,\n      ];\n    };\n\n    /**\n     * Creates a Long from its byte representation.\n     * @param {!Array.<number>} bytes Byte representation\n     * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n     * @param {boolean=} le Whether little or big endian, defaults to big endian\n     * @returns {Long} The corresponding Long value\n     */\n    Long.fromBytes = function fromBytes(bytes, unsigned, le) {\n      return le\n        ? Long.fromBytesLE(bytes, unsigned)\n        : Long.fromBytesBE(bytes, unsigned);\n    };\n\n    /**\n     * Creates a Long from its little endian byte representation.\n     * @param {!Array.<number>} bytes Little endian byte representation\n     * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n     * @returns {Long} The corresponding Long value\n     */\n    Long.fromBytesLE = function fromBytesLE(bytes, unsigned) {\n      return new Long(\n        bytes[0] | (bytes[1] << 8) | (bytes[2] << 16) | (bytes[3] << 24),\n        bytes[4] | (bytes[5] << 8) | (bytes[6] << 16) | (bytes[7] << 24),\n        unsigned,\n      );\n    };\n\n    /**\n     * Creates a Long from its big endian byte representation.\n     * @param {!Array.<number>} bytes Big endian byte representation\n     * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n     * @returns {Long} The corresponding Long value\n     */\n    Long.fromBytesBE = function fromBytesBE(bytes, unsigned) {\n      return new Long(\n        (bytes[4] << 24) | (bytes[5] << 16) | (bytes[6] << 8) | bytes[7],\n        (bytes[0] << 24) | (bytes[1] << 16) | (bytes[2] << 8) | bytes[3],\n        unsigned,\n      );\n    };\n\n    // Support conversion to/from BigInt where available\n    if (typeof BigInt === \"function\") {\n      /**\n       * Returns a Long representing the given big integer.\n       * @function\n       * @param {number} value The big integer value\n       * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n       * @returns {!Long} The corresponding Long value\n       */\n      Long.fromBigInt = function fromBigInt(value, unsigned) {\n        var lowBits = Number(BigInt.asIntN(32, value));\n        var highBits = Number(BigInt.asIntN(32, value >> BigInt(32)));\n        return fromBits(lowBits, highBits, unsigned);\n      };\n\n      // Override\n      Long.fromValue = function fromValueWithBigInt(value, unsigned) {\n        if (typeof value === \"bigint\") return Long.fromBigInt(value, unsigned);\n        return fromValue(value, unsigned);\n      };\n\n      /**\n       * Converts the Long to its big integer representation.\n       * @this {!Long}\n       * @returns {bigint}\n       */\n      LongPrototype.toBigInt = function toBigInt() {\n        var lowBigInt = BigInt(this.low >>> 0);\n        var highBigInt = BigInt(this.unsigned ? this.high >>> 0 : this.high);\n        return (highBigInt << BigInt(32)) | lowBigInt;\n      };\n    }\n    var _default = (_exports.default = Long);\n  },\n);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/long@5.3.2/node_modules/long/umd/index.js\n");

/***/ })

};
;