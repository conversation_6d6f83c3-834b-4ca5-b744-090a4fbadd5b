export interface DiscountTier {
  min: number;
  pct: number;
}

export interface DiscountResult {
  discountPercent: number;
  totalNet: number;
  progress: number;
  missing: number;
  nextTarget: number | null;
  currentTier: DiscountTier;
  nextTier: DiscountTier | null;
}

const DISCOUNT_TIERS: DiscountTier[] = [
  { min: 0, pct: 0 },
  { min: 1000, pct: 10 },
  { min: 2000, pct: 15 },
  { min: 3000, pct: 20 }
];

export function computeDiscount(totalGross: number): DiscountResult {
  const current = DISCOUNT_TIERS.filter(t => totalGross >= t.min).pop()!;
  const next = DISCOUNT_TIERS.find(t => t.min > current.min);
  
  const discountPercent = current.pct;
  const totalNet = Number((totalGross * (1 - discountPercent / 100)).toFixed(2));
  
  const progress = next 
    ? Math.max(0, Math.min(1, (totalGross - current.min) / (next.min - current.min)))
    : 1;
  
  const missing = next ? Math.max(0, next.min - totalGross) : 0;
  
  return {
    discountPercent,
    totalNet,
    progress,
    missing,
    nextTarget: next?.pct ?? null,
    currentTier: current,
    nextTier: next ?? null
  };
}

export function formatCurrency(value: number): string {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value);
}
