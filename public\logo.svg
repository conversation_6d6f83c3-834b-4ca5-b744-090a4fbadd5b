<svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>

  <!-- Outer circle -->
  <circle cx="50" cy="50" r="47" stroke="url(#goldGradient)" stroke-width="3" fill="none" filter="url(#glow)"/>

  <!-- Letter 'd' with elegant curves -->
  <path d="M25 30 Q25 25 30 25 L35 25 Q50 25 50 40 L50 60 Q50 75 35 75 L30 75 Q25 75 25 70 Z M30 35 L30 65 Q30 70 35 70 Q40 70 40 65 L40 35 Q40 30 35 30 Z"
        fill="url(#goldGradient)" filter="url(#glow)"/>

  <!-- Letter 'c' with elegant curves -->
  <path d="M60 40 Q60 25 70 25 Q80 25 80 35 L75 35 Q75 30 70 30 Q65 30 65 35 L65 65 Q65 70 70 70 Q75 70 75 65 L80 65 Q80 75 70 75 Q60 75 60 60 Z"
        fill="url(#goldGradient)" filter="url(#glow)"/>

  <!-- Decorative elements -->
  <circle cx="50" cy="20" r="2" fill="url(#goldGradient)" opacity="0.7"/>
  <circle cx="20" cy="50" r="2" fill="url(#goldGradient)" opacity="0.7"/>
  <circle cx="80" cy="50" r="2" fill="url(#goldGradient)" opacity="0.7"/>
  <circle cx="50" cy="80" r="2" fill="url(#goldGradient)" opacity="0.7"/>
</svg>
